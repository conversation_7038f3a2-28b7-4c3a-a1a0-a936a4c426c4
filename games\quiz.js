const { EmbedBuilder } = require('discord.js');
const { allowedRoleIds } = require('../config.json');
// Use SQL-based points manager
const { pointsManager } = require('../sql-points.js');

// Game timeout in milliseconds (10 seconds)
const GAME_TIMEOUT = 10000;

// Active games tracker
const activeGames = new Map();

// Quiz data structure
const quizData = {
  'اسرع': {
    name: 'لعبة الأسرع',
    words: [
      "تفاحة", "برتقال", "موز", "عنب", "كمثرى", "بطيخ", "خوخ", "رمان", "ليمون", "فراولة",
      "قلم", "مسطرة", "ممحاة", "مبراة", "دفتر", "كتاب", "كرسي", "طاولة", "لوح", "حقيبة",
      "سيارة", "دراجة", "قطار", "طائرة", "باخرة", "مركبة", "سفينة", "مطار", "ميناء", "شارع",
      "هاتف", "حاسوب", "لاب توب", "ماوس", "شاشة", "لوحة مفاتيح", "سماعة", "كاميرا", "مايكروفون", "روبوت",
      "باب", "نافذة", "جدار", "سقف", "مصباح", "شمعة", "ثلاجة", "فرن", "مروحة", "مكيف",
      "مفتاح", "قفل", "سلسلة", "ساعة", "مرآة", "فرشاة", "معجون", "صابون", "مقص", "مشط",
      "بيت", "غرفة", "مطبخ", "حمام", "حديقة", "سطح", "قبو", "شرفة", "ممر", "سلم",
      "مدرسة", "جامعة", "مكتبة", "فصل", "قاعة", "مستشفى", "صيدلية", "عيادة", "مسجد", "كنيسة",
      "قطة", "كلب", "أسد", "نمر", "ذئب", "ثعلب", "فيل", "زرافة", "حصان", "جمل",
      "دجاجة", "بطة", "وزة", "نعامة", "بقرة", "خروف", "ماعز", "غزال", "قرد", "نحلة",
      "سمكة", "حوت", "دولفين", "قرش", "سلحفاة", "تمساح", "ضفدع", "ثعبان", "عقرب", "صرصور",
      "ملعقة", "شوكة", "سكين", "طبق", "كأس", "إبريق", "غلاية", "طنجرة", "مقلاة", "فرن",
      "ماء", "عصير", "شاي", "قهوة", "لبن", "حليب", "كولا", "بيبسي", "مشروب", "سكر",
      "ملح", "فلفل", "كمون", "كركم", "زنجبيل", "قرفة", "خل", "زيت", "زبدة", "جبن",
      "رغيف", "خبز", "كعك", "بسكويت", "بيتزا", "برغر", "شوربة", "أرز", "معكرونة", "لحم",
      "دجاج", "سمك", "بيضة", "تمر", "زيتون", "بطاطس", "سلطة", "فول", "حمص", "عدس",
      "قلب", "عين", "رأس", "أنف", "فم", "يد", "رجل", "أذن", "رقبة", "ظهر",
      "سعادة", "فرح", "حزن", "غضب", "خوف", "حب", "راحة", "تعب", "ملل", "دهشة",
      "رياضة", "سباحة", "كرة", "سلة", "قدم", "تنس", "جري", "قفز", "جودو", "ملاكمة",
      "شمس", "قمر", "سماء", "نجمة", "كوكب", "غيوم", "مطر", "ثلج", "عاصفة", "رعد",
      "نار", "هواء", "تراب", "ماء", "جبل", "وادي", "نهر", "بحر", "جزيرة", "صحراء",
      "مدينة", "قرية", "بلد", "سوق", "مقهى", "مطعم", "مخبز", "محطة", "مركز", "منتزه",
      "مهنة", "طبيب", "معلم", "مهندس", "نجار", "حداد", "سائق", "مزارع", "شرطي", "جندي",
      "رسام", "كاتب", "مغني", "ممثل", "طباخ", "مذيع", "محامي", "قاضي", "باحث", "عامل"
    ]
  },
  'دين': {
    name: 'لعبة الدين',
    questions: [
      {
        image: 'https://i.imgur.com/example4.jpg',
        answers: ['مسجد', 'جامع', 'صلاة', 'mosque']
      },
      {
        image: 'https://i.imgur.com/example5.jpg',
        answers: ['قرآن', 'كتاب', 'مصحف', 'quran']
      },
      {
        image: 'https://i.imgur.com/example6.jpg',
        answers: ['كعبة', 'مكة', 'حج', 'kaaba']
      }
    ]
  },
 'اعلام': {
  name: 'لعبة الأعلام',
  questions: [
    { image: 'https://flagcdn.com/w320/sa.png', answers: ['السعودية', 'المملكة العربية السعودية', 'سعودية', 'saudi arabia'] },
    { image: 'https://flagcdn.com/w320/eg.png', answers: ['مصر', 'جمهورية مصر العربية', 'مصرية', 'egypt'] },
    { image: 'https://flagcdn.com/w320/ae.png', answers: ['الإمارات', 'دولة الإمارات', 'emirates', 'uae'] },
    { image: 'https://flagcdn.com/w320/kw.png', answers: ['الكويت', 'kuwait'] },
    { image: 'https://flagcdn.com/w320/qa.png', answers: ['قطر', 'qatar'] },
    { image: 'https://flagcdn.com/w320/bh.png', answers: ['البحرين', 'bahrain'] },
    { image: 'https://flagcdn.com/w320/om.png', answers: ['عمان', 'سلطنة عمان', 'oman'] },
    { image: 'https://flagcdn.com/w320/lb.png', answers: ['لبنان', 'lebanon'] },
    { image: 'https://flagcdn.com/w320/sy.png', answers: ['سوريا', 'syrian arab republic', 'syria'] },
    { image: 'https://flagcdn.com/w320/ye.png', answers: ['اليمن', 'yemen'] },
    { image: 'https://flagcdn.com/w320/jo.png', answers: ['الأردن', 'jordan'] },
    { image: 'https://flagcdn.com/w320/ps.png', answers: ['فلسطين', 'palestine'] },
    { image: 'https://flagcdn.com/w320/ma.png', answers: ['المغرب', 'morocco'] },
    { image: 'https://flagcdn.com/w320/tn.png', answers: ['تونس', 'tunisia'] },
    { image: 'https://flagcdn.com/w320/dz.png', answers: ['الجزائر', 'algeria'] },
    { image: 'https://flagcdn.com/w320/sd.png', answers: ['السودان', 'sudan'] },
    { image: 'https://flagcdn.com/w320/ly.png', answers: ['ليبيا', 'libya'] },
    { image: 'https://flagcdn.com/w320/iq.png', answers: ['العراق', 'iraq'] },
    { image: 'https://flagcdn.com/w320/tr.png', answers: ['تركيا', 'turkey'] },
    { image: 'https://flagcdn.com/w320/ir.png', answers: ['إيران', 'iran'] },
    { image: 'https://flagcdn.com/w320/af.png', answers: ['أفغانستان', 'afghanistan'] },
    { image: 'https://flagcdn.com/w320/pk.png', answers: ['باكستان', 'pakistan'] },
    { image: 'https://flagcdn.com/w320/in.png', answers: ['الهند', 'india'] },
    { image: 'https://flagcdn.com/w320/bd.png', answers: ['بنغلاديش', 'bangladesh'] },
    { image: 'https://flagcdn.com/w320/np.png', answers: ['نيبال', 'nepal'] },
    { image: 'https://flagcdn.com/w320/cn.png', answers: ['الصين', 'china'] },
    { image: 'https://flagcdn.com/w320/jp.png', answers: ['اليابان', 'japan'] },
    { image: 'https://flagcdn.com/w320/kr.png', answers: ['كوريا الجنوبية', 'south korea', 'korea'] },
    { image: 'https://flagcdn.com/w320/nk.png', answers: ['كوريا الشمالية', 'north korea'] },
    { image: 'https://flagcdn.com/w320/th.png', answers: ['تايلاند', 'thailand'] },
    { image: 'https://flagcdn.com/w320/vn.png', answers: ['فيتنام', 'vietnam'] },
    { image: 'https://flagcdn.com/w320/la.png', answers: ['لاوس', 'laos'] },
    { image: 'https://flagcdn.com/w320/mm.png', answers: ['ميانمار', 'myanmar', 'burma'] },
    { image: 'https://flagcdn.com/w320/mn.png', answers: ['منغوليا', 'mongolia'] },
    { image: 'https://flagcdn.com/w320/ru.png', answers: ['روسيا', 'russia'] },
    { image: 'https://flagcdn.com/w320/ua.png', answers: ['أوكرانيا', 'ukraine'] },
    { image: 'https://flagcdn.com/w320/by.png', answers: ['بيلاروسيا', 'belarus'] },
    { image: 'https://flagcdn.com/w320/pl.png', answers: ['بولندا', 'poland'] },
    { image: 'https://flagcdn.com/w320/de.png', answers: ['ألمانيا', 'germany'] },
    { image: 'https://flagcdn.com/w320/fr.png', answers: ['فرنسا', 'france'] },
    { image: 'https://flagcdn.com/w320/es.png', answers: ['إسبانيا', 'spain'] },
    { image: 'https://flagcdn.com/w320/pt.png', answers: ['البرتغال', 'portugal'] },
    { image: 'https://flagcdn.com/w320/it.png', answers: ['إيطاليا', 'italy'] },
    { image: 'https://flagcdn.com/w320/gb.png', answers: ['بريطانيا', 'المملكة المتحدة', 'uk', 'united kingdom', 'england'] },
    { image: 'https://flagcdn.com/w320/ie.png', answers: ['أيرلندا', 'ireland'] },
    { image: 'https://flagcdn.com/w320/is.png', answers: ['آيسلندا', 'iceland'] },
    { image: 'https://flagcdn.com/w320/no.png', answers: ['النرويج', 'norway'] },
    { image: 'https://flagcdn.com/w320/se.png', answers: ['السويد', 'sweden'] },
    { image: 'https://flagcdn.com/w320/fi.png', answers: ['فنلندا', 'finland'] },
    { image: 'https://flagcdn.com/w320/dk.png', answers: ['الدنمارك', 'denmark'] },
    { image: 'https://flagcdn.com/w320/nl.png', answers: ['هولندا', 'netherlands', 'holland'] },
    { image: 'https://flagcdn.com/w320/be.png', answers: ['بلجيكا', 'belgium'] },
    { image: 'https://flagcdn.com/w320/ch.png', answers: ['سويسرا', 'switzerland'] },
    { image: 'https://flagcdn.com/w320/at.png', answers: ['النمسا', 'austria'] },
    { image: 'https://flagcdn.com/w320/gr.png', answers: ['اليونان', 'greece'] },
    { image: 'https://flagcdn.com/w320/cz.png', answers: ['التشيك', 'czech republic'] },
    { image: 'https://flagcdn.com/w320/sk.png', answers: ['سلوفاكيا', 'slovakia'] },
    { image: 'https://flagcdn.com/w320/hu.png', answers: ['المجر', 'hungary'] },
    { image: 'https://flagcdn.com/w320/ro.png', answers: ['رومانيا', 'romania'] },
    { image: 'https://flagcdn.com/w320/bg.png', answers: ['بلغاريا', 'bulgaria'] },
    { image: 'https://flagcdn.com/w320/rs.png', answers: ['صربيا', 'serbia'] },
    { image: 'https://flagcdn.com/w320/hr.png', answers: ['كرواتيا', 'croatia'] },
    { image: 'https://flagcdn.com/w320/si.png', answers: ['سلوفينيا', 'slovenia'] },
    { image: 'https://flagcdn.com/w320/mk.png', answers: ['مقدونيا الشمالية', 'north macedonia'] },
    { image: 'https://flagcdn.com/w320/al.png', answers: ['ألبانيا', 'albania'] },
    { image: 'https://flagcdn.com/w320/mt.png', answers: ['مالطا', 'malta'] },
    { image: 'https://flagcdn.com/w320/cy.png', answers: ['قبرص', 'cyprus'] },
    { image: 'https://flagcdn.com/w320/ca.png', answers: ['كندا', 'canada'] },
    { image: 'https://flagcdn.com/w320/us.png', answers: ['أمريكا', 'الولايات المتحدة', 'usa', 'united states'] },
    { image: 'https://flagcdn.com/w320/mx.png', answers: ['المكسيك', 'mexico'] },
    { image: 'https://flagcdn.com/w320/br.png', answers: ['البرازيل', 'brazil'] },
    { image: 'https://flagcdn.com/w320/ar.png', answers: ['الأرجنتين', 'argentina'] },
    { image: 'https://flagcdn.com/w320/cl.png', answers: ['تشيلي', 'chile'] },
    { image: 'https://flagcdn.com/w320/co.png', answers: ['كولومبيا', 'colombia'] },
    { image: 'https://flagcdn.com/w320/pe.png', answers: ['بيرو', 'peru'] },
    { image: 'https://flagcdn.com/w320/ve.png', answers: ['فنزويلا', 'venezuela'] },
    { image: 'https://flagcdn.com/w320/uy.png', answers: ['أوروغواي', 'uruguay'] },
    { image: 'https://flagcdn.com/w320/py.png', answers: ['باراغواي', 'paraguay'] },
    { image: 'https://flagcdn.com/w320/bo.png', answers: ['بوليفيا', 'bolivia'] },
    { image: 'https://flagcdn.com/w320/ec.png', answers: ['الإكوادور', 'ecuador'] }
  ]
},
  'شخصية': {
    name: 'لعبة الشخصيات',
    questions: [
      {
        image: 'https://i.imgur.com/example10.jpg',
        answers: ['محمد صلاح', 'صلاح', 'مو صلاح', 'salah']
      },
      {
        image: 'https://i.imgur.com/example11.jpg',
        answers: ['كريستيانو رونالدو', 'رونالدو', 'كريستيانو', 'ronaldo']
      },
      {
        image: 'https://i.imgur.com/example12.jpg',
        answers: ['ليونيل ميسي', 'ميسي', 'messi']
      }
    ]
  },
  'ترجم': {
  name: 'لعبة الترجمة',
  questions: [
    { question: 'كتاب', answers: ['book'] },
    { question: 'سيارة', answers: ['car'] },
    { question: 'بيت', answers: ['house', 'home'] },
    { question: 'هاتف', answers: ['phone', 'mobile'] },
    { question: 'شجرة', answers: ['tree'] },
    { question: 'قلم', answers: ['pen'] },
    { question: 'كرسي', answers: ['chair'] },
    { question: 'حاسوب', answers: ['computer'] },
    { question: 'قطة', answers: ['cat'] },
    { question: 'كلب', answers: ['dog'] },
    { question: 'دراجة', answers: ['bicycle', 'bike'] },
    { question: 'تفاحة', answers: ['apple'] },
    { question: 'برتقال', answers: ['orange'] },
    { question: 'موز', answers: ['banana'] },
    { question: 'طائرة', answers: ['plane', 'airplane'] },
    { question: 'قطار', answers: ['train'] },
    { question: 'سفينة', answers: ['ship', 'boat'] },
    { question: 'زهرة', answers: ['flower'] },
    { question: 'قميص', answers: ['shirt'] },
    { question: 'حذاء', answers: ['shoes', 'shoe'] },
    { question: 'مطر', answers: ['rain'] },
    { question: 'شمس', answers: ['sun'] },
    { question: 'قمر', answers: ['moon'] },
    { question: 'نجمة', answers: ['star'] },
    { question: 'ثلج', answers: ['snow'] },
    { question: 'نار', answers: ['fire'] },
    { question: 'ماء', answers: ['water'] },
    { question: 'بحر', answers: ['sea'] },
    { question: 'نهر', answers: ['river'] },
    { question: 'جبل', answers: ['mountain'] },
    { question: 'صحراء', answers: ['desert'] },
    { question: 'سماء', answers: ['sky'] },
    { question: 'سحابة', answers: ['cloud'] },
    { question: 'طعام', answers: ['food'] },
    { question: 'خبز', answers: ['bread'] },
    { question: 'ملح', answers: ['salt'] },
    { question: 'سكر', answers: ['sugar'] },
    { question: 'حليب', answers: ['milk'] },
    { question: 'شاي', answers: ['tea'] },
    { question: 'قهوة', answers: ['coffee'] },
    { question: 'سمك', answers: ['fish'] },
    { question: 'لحم', answers: ['meat'] },
    { question: 'دجاج', answers: ['chicken'] },
    { question: 'بيض', answers: ['egg'] },
    { question: 'جبن', answers: ['cheese'] },
    { question: 'ملعقة', answers: ['spoon'] },
    { question: 'شوكة', answers: ['fork'] },
    { question: 'سكين', answers: ['knife'] },
    { question: 'طبق', answers: ['plate', 'dish'] },
    { question: 'كوب', answers: ['cup', 'mug'] },
    { question: 'زجاجة', answers: ['bottle'] },
    { question: 'باب', answers: ['door'] },
    { question: 'نافذة', answers: ['window'] },
    { question: 'جدار', answers: ['wall'] },
    { question: 'سقف', answers: ['ceiling', 'roof'] },
    { question: 'أرض', answers: ['floor', 'ground'] },
    { question: 'غرفة', answers: ['room'] },
    { question: 'سرير', answers: ['bed'] },
    { question: 'وسادة', answers: ['pillow'] },
    { question: 'بطانية', answers: ['blanket'] },
    { question: 'مكتب', answers: ['desk'] },
    { question: 'مدرسة', answers: ['school'] },
    { question: 'جامعة', answers: ['university'] },
    { question: 'كتاب مدرسي', answers: ['textbook'] },
    { question: 'حقيبة', answers: ['bag'] },
    { question: 'خريطة', answers: ['map'] },
    { question: 'ساعة', answers: ['clock', 'watch'] },
    { question: 'وقت', answers: ['time'] },
    { question: 'يوم', answers: ['day'] },
    { question: 'أسبوع', answers: ['week'] },
    { question: 'شهر', answers: ['month'] },
    { question: 'سنة', answers: ['year'] },
    { question: 'أمس', answers: ['yesterday'] },
    { question: 'اليوم', answers: ['today'] },
    { question: 'غدًا', answers: ['tomorrow'] },
    { question: 'صباح', answers: ['morning'] },
    { question: 'مساء', answers: ['evening'] },
    { question: 'ليل', answers: ['night'] },
    { question: 'مدينة', answers: ['city'] },
    { question: 'قرية', answers: ['village'] },
    { question: 'شارع', answers: ['street'] },
    { question: 'طريق', answers: ['road'] },
    { question: 'جسر', answers: ['bridge'] },
    { question: 'مطار', answers: ['airport'] },
    { question: 'محطة', answers: ['station'] },
    { question: 'مستشفى', answers: ['hospital'] },
    { question: 'صيدلية', answers: ['pharmacy'] },
    { question: 'سوق', answers: ['market'] },
    { question: 'متجر', answers: ['store', 'shop'] },
    { question: 'مكتبة', answers: ['library'] },
    { question: 'مسرح', answers: ['theater'] },
    { question: 'حديقة', answers: ['garden', 'park'] },
    { question: 'ملعب', answers: ['stadium'] },
    { question: 'مسبح', answers: ['pool'] },
    { question: 'شاطئ', answers: ['beach'] },
    { question: 'مغسلة', answers: ['sink'] },
    { question: 'حمام', answers: ['bathroom', 'toilet'] },
    { question: 'مطبخ', answers: ['kitchen'] },
    { question: 'غداء', answers: ['lunch'] },
    { question: 'فطور', answers: ['breakfast'] },
    { question: 'عشاء', answers: ['dinner'] },
    { question: 'حلوى', answers: ['dessert', 'candy'] },
    { question: 'شوكولاتة', answers: ['chocolate'] },
    { question: 'عصير', answers: ['juice'] },
    { question: 'صودا', answers: ['soda'] },
    { question: 'ماء غازي', answers: ['sparkling water'] },
    { question: 'حقيبة سفر', answers: ['suitcase'] },
    { question: 'كاميرا', answers: ['camera'] },
    { question: 'تذكرة', answers: ['ticket'] },
    { question: 'فندق', answers: ['hotel'] },
    { question: 'إجازة', answers: ['vacation', 'holiday'] },
    { question: 'بحيرة', answers: ['lake'] },
    { question: 'غابة', answers: ['forest'] },
    { question: 'جزيرة', answers: ['island'] },
    { question: 'حوت', answers: ['whale'] },
    { question: 'دلفين', answers: ['dolphin'] },
    { question: 'سمكة قرش', answers: ['shark'] },
    { question: 'أسد', answers: ['lion'] },
    { question: 'نمر', answers: ['tiger'] },
    { question: 'فيل', answers: ['elephant'] },
    { question: 'زرافة', answers: ['giraffe'] },
    { question: 'قرد', answers: ['monkey'] },
    { question: 'ثعلب', answers: ['fox'] },
    { question: 'ذئب', answers: ['wolf'] },
    { question: 'دب', answers: ['bear'] },
    { question: 'ثعبان', answers: ['snake'] },
    { question: 'عقرب', answers: ['scorpion'] },
    { question: 'نحلة', answers: ['bee'] },
    { question: 'فراشة', answers: ['butterfly'] },
    { question: 'عنكبوت', answers: ['spider'] },
    { question: 'نملة', answers: ['ant'] },
    { question: 'بعوضة', answers: ['mosquito'] },
    { question: 'سلحفاة', answers: ['turtle', 'tortoise'] },
    { question: 'ضفدع', answers: ['frog'] },
    { question: 'بطة', answers: ['duck'] },
    { question: 'ديك', answers: ['rooster'] },
    { question: 'بقرة', answers: ['cow'] },
    { question: 'خروف', answers: ['sheep'] },
    { question: 'ماعز', answers: ['goat'] },
    { question: 'حصان', answers: ['horse'] },
    { question: 'حمار', answers: ['donkey'] },
    { question: 'جمل', answers: ['camel'] }
  ]
},
  'عواصم': {
    name: 'لعبة العواصم',
    questions: [
      {
        image: 'https://i.imgur.com/example16.jpg',
        answers: ['الرياض', 'رياض', 'riyadh']
      },
      {
        image: 'https://i.imgur.com/example17.jpg',
        answers: ['القاهرة', 'قاهرة', 'cairo']
      },
      {
        image: 'https://i.imgur.com/example18.jpg',
        answers: ['دبي', 'dubai', 'الإمارات']
      }
    ]
  },
  'تمويه': {
    name: 'لعبة التمويه',
    questions: [
      {
        image: 'https://i.imgur.com/example19.jpg',
        answers: ['نمر', 'تايجر', 'نمور', 'tiger']
      },
      {
        image: 'https://i.imgur.com/example20.jpg',
        answers: ['فراشة', 'butterfly', 'فراش']
      },
      {
        image: 'https://i.imgur.com/example21.jpg',
        answers: ['حرباء', 'chameleon', 'سحلية']
      }
    ]
  },
  'رتب': {
    name: 'لعبة ترتيب الكلمات',
    questions: [
      {
        image: 'https://i.imgur.com/example22.jpg',
        answers: ['مدرسة', 'school', 'تعليم']
      },
      {
        image: 'https://i.imgur.com/example23.jpg',
        answers: ['مستشفى', 'hospital', 'طبيب']
      },
      {
        image: 'https://i.imgur.com/example24.jpg',
        answers: ['مطعم', 'restaurant', 'طعام']
      }
    ]
  },
 'براند': {
  name: 'لعبة العلامات التجارية',
  questions: [
    // --- ملابس وأزياء ---
    { image: 'https://logo.clearbit.com/nike.com', answers: ['نايك', 'nike', 'نايكي'] },
    { image: 'https://logo.clearbit.com/adidas.com', answers: ['أديداس', 'adidas'] },
    { image: 'https://logo.clearbit.com/zara.com', answers: ['زارا', 'zara'] },
    { image: 'https://logo.clearbit.com/h&m.com', answers: ['اتش آند ام', 'h&m'] },
    { image: 'https://logo.clearbit.com/gucci.com', answers: ['غوتشي', 'gucci'] },
    { image: 'https://logo.clearbit.com/louisvuitton.com', answers: ['لويس فويتون', 'louis vuitton'] },
    { image: 'https://logo.clearbit.com/chanel.com', answers: ['شانيل', 'chanel'] },
    { image: 'https://logo.clearbit.com/prada.com', answers: ['برادا', 'prada'] },
    { image: 'https://logo.clearbit.com/versace.com', answers: ['فيرساتشي', 'versace'] },
    { image: 'https://logo.clearbit.com/calvinklein.com', answers: ['كالفن كلاين', 'calvin klein'] },
    { image: 'https://logo.clearbit.com/dior.com', answers: ['ديور', 'dior'] },
    { image: 'https://logo.clearbit.com/armani.com', answers: ['أرماني', 'armani'] },
    { image: 'https://logo.clearbit.com/ray-ban.com', answers: ['راي بان', 'ray-ban'] },
    { image: 'https://logo.clearbit.com/tomford.com', answers: ['توم فورد', 'tom ford'] },
    { image: 'https://logo.clearbit.com/underarmour.com', answers: ['أندر آرمور', 'under armour'] },
    { image: 'https://logo.clearbit.com/levis.com', answers: ['ليفيس', 'levis'] },
    { image: 'https://logo.clearbit.com/converse.com', answers: ['كونفرس', 'converse'] },
    { image: 'https://logo.clearbit.com/puma.com', answers: ['بوما', 'puma'] },
    { image: 'https://logo.clearbit.com/newbalance.com', answers: ['نيو بالانس', 'new balance'] },

    // --- ميك أب ومستحضرات تجميل ---
    { image: 'https://logo.clearbit.com/maybelline.com', answers: ['مايبيلين', 'maybelline'] },
    { image: 'https://logo.clearbit.com/loreal.com', answers: ['لوريال', 'loreal'] },
    { image: 'https://logo.clearbit.com/maccosmetics.com', answers: ['ماك', 'mac cosmetics'] },
    { image: 'https://logo.clearbit.com/clinique.com', answers: ['كلينيك', 'clinique'] },
    { image: 'https://logo.clearbit.com/estee-lauder.com', answers: ['ايستي لودر', 'estee lauder'] },
    { image: 'https://logo.clearbit.com/sephora.com', answers: ['سيفورا', 'sephora'] },
    { image: 'https://logo.clearbit.com/benefitcosmetics.com', answers: ['بيني فيت', 'benefit'] },
    { image: 'https://logo.clearbit.com/urban-decay.com', answers: ['أربن ديكاي', 'urban decay'] },

    // --- سيارات ---
    { image: 'https://logo.clearbit.com/tesla.com', answers: ['تسلا', 'tesla'] },
    { image: 'https://logo.clearbit.com/ford.com', answers: ['فورد', 'ford'] },
    { image: 'https://logo.clearbit.com/chevrolet.com', answers: ['شيفروليه', 'chevrolet'] },
    { image: 'https://logo.clearbit.com/bmw.com', answers: ['بي إم دبليو', 'bmw'] },
    { image: 'https://logo.clearbit.com/audi.com', answers: ['أودي', 'audi'] },
    { image: 'https://logo.clearbit.com/mercedes-benz.com', answers: ['مرسيدس', 'mercedes benz'] },
    { image: 'https://logo.clearbit.com/toyota.com', answers: ['تويوتا', 'toyota'] },
    { image: 'https://logo.clearbit.com/honda.com', answers: ['هوندا', 'honda'] },
    { image: 'https://logo.clearbit.com/volkswagen.com', answers: ['فولكس فاجن', 'volkswagen'] },
    { image: 'https://logo.clearbit.com/nissan-global.com', answers: ['نيسان', 'nissan'] },
    { image: 'https://logo.clearbit.com/jeep.com', answers: ['جيب', 'jeep'] },
    { image: 'https://logo.clearbit.com/lexus.com', answers: ['لكزس', 'lexus'] },
    { image: 'https://logo.clearbit.com/mazda.com', answers: ['مازدا', 'mazda'] },
    { image: 'https://logo.clearbit.com/subaru.com', answers: ['سوبارو', 'subaru'] },
    { image: 'https://logo.clearbit.com/hyundai.com', answers: ['هيونداي', 'hyundai'] },
    { image: 'https://logo.clearbit.com/kia.com', answers: ['كيا', 'kia'] },
    { image: 'https://logo.clearbit.com/volvo.com', answers: ['فولفو', 'volvo'] },
    { image: 'https://logo.clearbit.com/mini.com', answers: ['ميني', 'mini'] },

    // --- طيران وشركات الطيران ---
    { image: 'https://logo.clearbit.com/airbus.com', answers: ['ايرباص', 'airbus'] },
    { image: 'https://logo.clearbit.com/boeing.com', answers: ['بوينج', 'boeing'] },
    { image: 'https://logo.clearbit.com/emirates.com', answers: ['الإمارات', 'emirates'] },
    { image: 'https://logo.clearbit.com/qatarairways.com', answers: ['القطرية', 'qatar airways'] },
    { image: 'https://logo.clearbit.com/etihad.com', answers: ['الاتحاد', 'etihad'] },
    { image: 'https://logo.clearbit.com/lufthansa.com', answers: ['لوفتهانزا', 'lufthansa'] },
    { image: 'https://logo.clearbit.com/britishairways.com', answers: ['الخطوط البريطانية', 'british airways'] },
    { image: 'https://logo.clearbit.com/singaporeair.com', answers: ['الخطوط السنغافورية', 'singapore airlines'] },
    { image: 'https://logo.clearbit.com/gulfair.com', answers: ['جلف إير', 'gulf air'] },
    { image: 'https://logo.clearbit.com/jetblue.com', answers: ['جيت بلو', 'jetblue'] },

    // --- تقنية وإلكترونيات ---
    { image: 'https://logo.clearbit.com/apple.com', answers: ['أبل', 'apple'] },
    { image: 'https://logo.clearbit.com/google.com', answers: ['جوجل', 'google'] },
    { image: 'https://logo.clearbit.com/microsoft.com', answers: ['مايكروسوفت', 'microsoft'] },
    { image: 'https://logo.clearbit.com/samsung.com', answers: ['سامسونج', 'samsung'] },
    { image: 'https://logo.clearbit.com/sony.com', answers: ['سوني', 'sony'] },
    { image: 'https://logo.clearbit.com/intel.com', answers: ['إنتل', 'intel'] },
    { image: 'https://logo.clearbit.com/nvidia.com', answers: ['نفيديا', 'nvidia'] },
    { image: 'https://logo.clearbit.com/amazon.com', answers: ['أمازون', 'amazon'] },
    { image: 'https://logo.clearbit.com/facebook.com', answers: ['فيسبوك', 'facebook'] },
    { image: 'https://logo.clearbit.com/instagram.com', answers: ['إنستغرام', 'instagram'] },
    { image: 'https://logo.clearbit.com/twitter.com', answers: ['تويتر', 'twitter'] },
    { image: 'https://logo.clearbit.com/linkedin.com', answers: ['لينكدإن', 'linkedin'] },
    { image: 'https://logo.clearbit.com/tesla.com', answers: ['تسلا', 'tesla'] },
    { image: 'https://logo.clearbit.com/netflix.com', answers: ['نتفليكس', 'netflix'] },
    { image: 'https://logo.clearbit.com/spotify.com', answers: ['سبوتيفاي', 'spotify'] },

    // --- أطعمة ومشروبات ---
    { image: 'https://logo.clearbit.com/cocacola.com', answers: ['كوكاكولا', 'cocacola'] },
    { image: 'https://logo.clearbit.com/pepsi.com', answers: ['بيبسي', 'pepsi'] },
    { image: 'https://logo.clearbit.com/starbucks.com', answers: ['ستاربكس', 'starbucks'] },
    { image: 'https://logo.clearbit.com/mcdonalds.com', answers: ['ماكدونالدز', 'mcdonalds'] },
    { image: 'https://logo.clearbit.com/kfc.com', answers: ['كنتاكي', 'kfc'] },
    { image: 'https://logo.clearbit.com/dunkindonuts.com', answers: ['دانكن دوناتس', 'dunkin donuts'] },
    { image: 'https://logo.clearbit.com/pizzahut.com', answers: ['بيتزا هت', 'pizza hut'] },
    { image: 'https://logo.clearbit.com/dominos.com', answers: ['دومينوز', 'dominos'] },
    { image: 'https://logo.clearbit.com/tacobell.com', answers: ['تاكو بيل', 'tacobell'] },
    { image: 'https://logo.clearbit.com/subway.com', answers: ['صب واي', 'subway'] },

    // --- رياضة وأندية ---
    { image: 'https://logo.clearbit.com/nfl.com', answers: ['الدوري الوطني لكرة القدم الأمريكية', 'nfl'] },
    { image: 'https://logo.clearbit.com/nba.com', answers: ['الرابطة الوطنية لكرة السلة', 'nba'] },
    { image: 'https://logo.clearbit.com/fifa.com', answers: ['فيفا', 'fifa'] },
    { image: 'https://logo.clearbit.com/espn.com', answers: ['ايه اس بي ان', 'espn'] },
    { image: 'https://logo.clearbit.com/adidas.com', answers: ['أديداس', 'adidas'] },
    { image: 'https://logo.clearbit.com/puma.com', answers: ['بوما', 'puma'] },

    // --- شركات توصيل ونقل ---
    { image: 'https://logo.clearbit.com/uber.com', answers: ['أوبر', 'uber'] },
    { image: 'https://logo.clearbit.com/lyft.com', answers: ['ليفت', 'lyft'] },
    { image: 'https://logo.clearbit.com/fedex.com', answers: ['فيدكس', 'fedex'] },
    { image: 'https://logo.clearbit.com/dhl.com', answers: ['دي إتش إل', 'dhl'] },
    { image: 'https://logo.clearbit.com/ups.com', answers: ['يو بي إس', 'ups'] },

    // --- ألعاب وأجهزة ألعاب ---
        { image: 'https://logo.clearbit.com/ea.com', answers: ['إي إيه', 'ea', 'electronic arts'] },
    { image: 'https://logo.clearbit.com/activision.com', answers: ['أكتيفيجن', 'activision'] },
    { image: 'https://logo.clearbit.com/ubisoft.com', answers: ['يوبيسوفت', 'ubisoft'] },
    { image: 'https://logo.clearbit.com/rockstargames.com', answers: ['روكستار', 'rockstar'] },
    { image: 'https://logo.clearbit.com/epicgames.com', answers: ['إبك جيمز', 'epic games'] },
    { image: 'https://logo.clearbit.com/bethesda.net', answers: ['بيثيسدا', 'bethesda'] },
    { image: 'https://logo.clearbit.com/valvesoftware.com', answers: ['فالڤ', 'valve'] },
    { image: 'https://logo.clearbit.com/square-enix.com', answers: ['سكوير إنيكس', 'square enix'] },
    { image: 'https://logo.clearbit.com/capcom.com', answers: ['كابكوم', 'capcom'] },
    { image: 'https://logo.clearbit.com/sega.com', answers: ['سيغا', 'sega'] },
    { image: 'https://logo.clearbit.com/konami.com', answers: ['كونامي', 'konami'] },
    { image: 'https://logo.clearbit.com/riotgames.com', answers: ['رايوت جيمز', 'riot games'] },
    { image: 'https://logo.clearbit.com/mojang.com', answers: ['موجانج', 'mojang'] },
    { image: 'https://logo.clearbit.com/garena.com', answers: ['جارينا', 'garena'] },
    { image: 'https://logo.clearbit.com/insomniacgames.com', answers: ['إنسومنياك', 'insomniac'] },
    { image: 'https://logo.clearbit.com/343industries.com', answers: ['343 إندستريز', '343 industries'] },
    { image: 'https://logo.clearbit.com/take2games.com', answers: ['تيك تو', 'take two'] },
    { image: 'https://logo.clearbit.com/bungie.net', answers: ['بانجي', 'bungie'] },
    { image: 'https://logo.clearbit.com/gearboxsoftware.com', answers: ['جيربوكس', 'gearbox'] },
    { image: 'https://logo.clearbit.com/levelupstudios.com', answers: ['ليفلب ستوديوز', 'levelup studios'] },

    // ألعاب مشهورة (ممكن تكتبها كعلامات تجارية)
    { image: '', answers: ['ماينكرافت', 'minecraft'] },
    { image: '', answers: ['فورتنايت', 'fortnite'] },
    { image: '', answers: ['كول أوف ديوتي', 'call of duty'] },
    { image: '', answers: ['ليج أوف ليجيندز', 'league of legends'] },
    { image: '', answers: ['دوتا 2', 'dota 2'] },
    { image: '', answers: ['رزد ديد ريدمبشن', 'red dead redemption'] },
    { image: '', answers: ['أساسنز كريد', 'assassins creed'] },
    { image: '', answers: ['فيفا', 'fifa'] },
    { image: '', answers: ['سبنر', 'spinners'] },
    { image: '', answers: ['سونيك', 'sonic'] },
    { image: '', answers: ['هيتمان', 'hitman'] },
    { image: '', answers: ['غران توريسمو', 'gran turismo'] },
    { image: '', answers: ['توم كلانسي', 'tom clancy'] },
    { image: '', answers: ['ميتال جير', 'metal gear'] },
    { image: '', answers: ['فاينل فانتسي', 'final fantasy'] },
    { image: '', answers: ['روكيت ليغ', 'rocket league'] },
    { image: '', answers: ['سبلاش داماج', 'splatoon'] },
    { image: '', answers: ['كرش بانديكوت', 'crash bandicoot'] },
    { image: '', answers: ['باتل فيلد', 'battlefield'] },

    // أجهزة ألعاب
    { image: 'https://logo.clearbit.com/nintendo.com', answers: ['نينتندو', 'nintendo'] },
    { image: 'https://logo.clearbit.com/sony.com', answers: ['بلاي ستيشن', 'playstation'] },
    { image: 'https://logo.clearbit.com/microsoft.com', answers: ['اكس بوكس', 'xbox'] },
    { image: 'https://logo.clearbit.com/steam.com', answers: ['ستيم', 'steam'] },
    { image: 'https://logo.clearbit.com/atari.com', answers: ['أتاري', 'atari'] },
    { image: 'https://logo.clearbit.com/logitech.com', answers: ['لوجيتيك', 'logitech'] },
    { image: 'https://logo.clearbit.com/razer.com', answers: ['رايزر', 'razer'] },
    { image: 'https://logo.clearbit.com/corsair.com', answers: ['كورساير', 'corsair'] },
    { image: 'https://logo.clearbit.com/sega.com', answers: ['سيغا', 'sega'] },
    { image: 'https://logo.clearbit.com/valvesoftware.com', answers: ['فالڤ', 'valve'] },

    // ألعاب قديمة وكلاسيكية
    { image: '', answers: ['باك مان', 'pacman'] },
    { image: '', answers: ['تيترس', 'tetris'] },
    { image: '', answers: ['سوبر ماريو', 'super mario'] },
    { image: '', answers: ['دونكي كونغ', 'donkey kong'] },
    { image: '', answers: ['ميترويد', 'metroid'] },
    { image: '', answers: ['ذا ليجند أوف زيلدا', 'legend of zelda'] },

    // ألعاب موبايل شهيرة
    { image: '', answers: ['كلاش أوف كلانس', 'clash of clans'] },
    { image: '', answers: ['بوبجي', 'pubg'] },
    { image: '', answers: ['كول أوف ديوتي موبايل', 'call of duty mobile'] },
    { image: '', answers: ['فري فاير', 'free fire'] },
    { image: '', answers: ['هينتراند', 'hinterland'] },

    // استوديوهات صغيرة وشركات ناشئة
    { image: 'https://logo.clearbit.com/thatgamecompany.com', answers: ['ذات جيم كومباني', 'thatgamecompany'] },
    { image: 'https://logo.clearbit.com/hellogames.com', answers: ['هيلو جيمز', 'hello games'] },
    { image: 'https://logo.clearbit.com/iam8bit.com', answers: ['آي ايه أم 8 بيت', 'iam8bit'] },
    { image: 'https://logo.clearbit.com/bluehole.net', answers: ['بلو هول', 'bluehole'] },

    // --- متاجر وتسوق ---
    { image: 'https://logo.clearbit.com/walmart.com', answers: ['وول مارت', 'walmart'] },
    { image: 'https://logo.clearbit.com/target.com', answers: ['تارجت', 'target'] }
    ]
  }
};

// Check if user has permission to start games
function hasPermission(member) {
  if (member.permissions.has('Administrator')) {
    return true;
  }
  return allowedRoleIds.some(roleId => member.roles.cache.has(roleId));
}

// Normalize answer for comparison
function normalizeAnswer(answer) {
  return answer.toLowerCase().trim().replace(/\s+/g, ' ');
}

// Start a quiz game
async function startQuizGame(message, gameType) {
  try {
    console.log(`🎮 startQuizGame called with gameType: "${gameType}"`);
    // Check permissions
    if (!hasPermission(message.member)) {
      await message.reply('❌ **ليس لديك الإذن لبدء الألعاب.**');
      return;
    }

    // Check if there's already an active game in this channel
    if (activeGames.has(message.channel.id)) {
      await message.reply('❌ **يوجد لعبة نشطة بالفعل في هذه القناة. انتظر حتى تنتهي.**');
      return;
    }

    // Get game data
    const gameData = quizData[gameType];
    if (!gameData) {
      await message.reply('❌ **هذه اللعبة غير متوفرة حالياً.**');
      return;
    }

    // Check if game has valid data (words for اسرع, questions for others)
    if (gameType === 'اسرع') {
      if (!gameData.words || gameData.words.length === 0) {
        await message.reply('❌ **لا توجد كلمات متاحة لهذه اللعبة.**');
        return;
      }
    } else {
      if (!gameData.questions || gameData.questions.length === 0) {
        await message.reply('❌ **لا توجد أسئلة متاحة لهذه اللعبة.**');
        return;
      }
    }

    // Select random word for اسرع game or random question for other games
    let randomWord, randomQuestion;
    if (gameType === 'اسرع') {
      randomWord = gameData.words[Math.floor(Math.random() * gameData.words.length)];

      // Send the word to type with embed
      const gameEmbed = new EmbedBuilder()
        .setColor('#00FF00') // Green color like the first image
        .setAuthor({
          name: message.author.displayName,
          iconURL: message.author.displayAvatarURL()
        })
        .setDescription(`# ${randomWord}\nلديك **10 ثانية** لكتابة الجملة بسرعة`)
        .setFooter({
          text: 'SH Games',
          iconURL: 'https://cdn.discordapp.com/attachments/1372626105839915038/1396642152439877784/a_afc91405fdc3ba5a738bf5ef72ed7ca9.gif?ex=687ed3dd&is=687d825d&hm=814622da86eb0beaf0d4c04ab4c35c33cec516f514a30107a7958be7fe6f61bc&' // You can replace with your bot's icon
        });

      await message.channel.send({ embeds: [gameEmbed] });
    } else {
      randomQuestion = gameData.questions[Math.floor(Math.random() * gameData.questions.length)];

      // Send the image
      await message.channel.send({
        content: `🎮 **${gameData.name}**\n⏰ لديكم 10 ثواني للإجابة!`,
        files: [randomQuestion.image]
      });
    }

    // Track game start time
    const startTime = Date.now();

    // Set up active game
    activeGames.set(message.channel.id, {
      gameType,
      question: randomQuestion,
      word: randomWord,
      startTime,
      starterId: message.author.id
    });

    // Set up message collector
    const filter = (msg) => !msg.author.bot && msg.channel.id === message.channel.id;
    const collector = message.channel.createMessageCollector({
      filter,
      time: GAME_TIMEOUT
    });

    collector.on('collect', async (msg) => {
      const userAnswer = normalizeAnswer(msg.content);
      let isCorrect = false;

      if (gameType === 'اسرع') {
        // For اسرع game, check if user typed the exact word
        isCorrect = userAnswer === normalizeAnswer(randomWord);
      } else {
        // For other games, check against multiple possible answers
        const correctAnswers = randomQuestion.answers.map(answer => normalizeAnswer(answer));
        isCorrect = correctAnswers.includes(userAnswer);
      }

      // Check if answer is correct
      if (isCorrect) {
        const endTime = Date.now();
        const timeTaken = ((endTime - startTime) / 1000).toFixed(2);

        // Award point to winner
        const newPoints = await pointsManager.awardWin(msg.author.id, gameType);

        // Send success embed matching the style
        const successEmbed = new EmbedBuilder()
          .setColor('#00FF00') // Green color for success
          .setAuthor({
            name: msg.author.displayName,
            iconURL: msg.author.displayAvatarURL()
          })
          .setDescription(`# ${gameType === 'اسرع' ? randomWord : 'إجابة صحيحة'}\n🏆 **فائز!** لديك الآن **${newPoints}** نقطة\n⏱️ **الوقت:** ${timeTaken} ثانية`)
          .setFooter({
            text: 'SH Games',
            iconURL: 'https://cdn.discordapp.com/attachments/1372626105839915038/1396642152439877784/a_afc91405fdc3ba5a738bf5ef72ed7ca9.gif?ex=687ed3dd&is=687d825d&hm=814622da86eb0beaf0d4c04ab4c35c33cec516f514a30107a7958be7fe6f61bc&'
          });

        await message.channel.send({ embeds: [successEmbed] });

        // Stop collector and remove active game
        collector.stop('winner');
        activeGames.delete(message.channel.id);
      }
    });

    collector.on('end', async (collected, reason) => {
      if (reason !== 'winner') {
        if (gameType === 'اسرع') {
          // For اسرع game, show failure embed with red color
          const failureEmbed = new EmbedBuilder()
            .setColor('#FF0000') // Red color for failure
            .setAuthor({
              name: 'SH Games',
              iconURL: 'https://cdn.discordapp.com/attachments/1372626105839915038/1396642152439877784/a_afc91405fdc3ba5a738bf5ef72ed7ca9.gif?ex=687ed3dd&is=687d825d&hm=814622da86eb0beaf0d4c04ab4c35c33cec516f514a30107a7958be7fe6f61bc&'
            })
            .setDescription(`❌ لم يتمكن أحد من كتابة الكلمة **(${randomWord})** في الوقت المحدد`);

          await message.channel.send({ embeds: [failureEmbed] });
        } else {
          // For other games, show correct answers
          const correctAnswersList = randomQuestion.answers.map(answer => `- ${answer}`).join('\n');
          await message.channel.send(
            `انتهى الوقت، لم يفز أحد هذه المرة\n:الإجابات الصحيحة\n${correctAnswersList}`
          );
        }
      }

      // Clean up
      activeGames.delete(message.channel.id);
    });

  } catch (error) {
    console.error('Error in startQuizGame:', error);
    await message.reply('❌ **حدث خطأ أثناء بدء اللعبة.**');
    activeGames.delete(message.channel.id);
  }
}

// Handle quiz commands
async function handleQuizCommand(message, command) {
  console.log(`🎮 handleQuizCommand called with command: "${command}"`);
  const gameType = command.replace('+', '');
  console.log(`🎮 Extracted gameType: "${gameType}"`);
  console.log(`🎮 Available quiz games:`, Object.keys(quizData));

  if (quizData[gameType]) {
    console.log(`✅ Game found! Starting ${gameType} game...`);
    await startQuizGame(message, gameType);
  } else {
    console.log(`❌ Game not found! gameType: "${gameType}"`);
    await message.reply('❌ **هذه اللعبة غير متوفرة حالياً.**');
  }
}

// Get active games status
function getActiveGamesStatus() {
  return activeGames.size;
}

module.exports = {
  handleQuizCommand,
  getActiveGamesStatus,
  quizData
};
