# 🔐 نظام إدارة صلاحيات الألعاب

## 📋 نظرة عامة

تم إضافة نظام شامل لإدارة صلاحيات الألعاب يسمح لك بالتحكم في من يمكنه استخدام الألعاب التالية:
- 🎲 **روليت** (Roulette)
- 🪑 **كراسي** (Musical Chairs)
- 🕵️ **مافيا** (Mafia)
- 🔎 **جاسوس** (Spy)

## 🎮 الأوامر المتاحة

### إضافة صلاحية
```
/اضافة_صلاحية @user
/اضافة_صلاحية @role
/add_permission @user
/add_permission @role
```

**الوصف:** يضيف صلاحية استخدام جميع الألعاب للمستخدم أو الرول المحدد.

**مثال:**
```
/اضافة_صلاحية @أحمد
/اضافة_صلاحية @مشرف_الألعاب
```

### حذف صلاحية
```
/حذف_صلاحية @user
/حذف_صلاحية @role
/remove_permission @user
/remove_permission @role
```

**الوصف:** يحذف صلاحية استخدام الألعاب من المستخدم أو الرول المحدد.

**مثال:**
```
/حذف_صلاحية @أحمد
/حذف_صلاحية @مشرف_الألعاب
```

### عرض قائمة الصلاحيات
```
/قائمة_الصلاحيات
/list_permissions
```

**الوصف:** يعرض قائمة شاملة بجميع المستخدمين والأدوار الذين لديهم صلاحية استخدام الألعاب.

## 🔒 متطلبات الصلاحية

### من يمكنه إدارة الصلاحيات؟
- **المشرفين** (المستخدمين الذين لديهم صلاحية Administrator)
- **المستخدمين الذين لديهم صلاحية بالفعل** (المدرجين في قائمة allowedRoleIds)

### من يمكنه استخدام الألعاب؟
- **المستخدمين المضافين مباشرة** عبر أمر إضافة الصلاحية
- **المستخدمين الذين لديهم أدوار مضافة** عبر أمر إضافة الصلاحية

## 📊 ميزات النظام

### 1. **المرونة الكاملة**
- إضافة مستخدمين فرديين أو أدوار كاملة
- إدارة الصلاحيات في الوقت الفعلي دون إعادة تشغيل البوت

### 2. **الأمان**
- فقط المشرفين والمستخدمين المصرح لهم يمكنهم إدارة الصلاحيات
- حفظ تلقائي للتغييرات في ملف config.json

### 3. **سهولة الاستخدام**
- أوامر باللغة العربية والإنجليزية
- رسائل واضحة ومفصلة للنجاح والأخطاء
- عرض إحصائيات شاملة

### 4. **التحديث التلقائي**
- تحديث فوري للصلاحيات دون الحاجة لإعادة تشغيل البوت
- حفظ دائم في ملف التكوين

## 🎯 أمثلة عملية

### إضافة مشرف ألعاب جديد
```
/اضافة_صلاحية @مشرف_الألعاب
```
**النتيجة:** جميع المستخدمين الذين لديهم رول "مشرف_الألعاب" يمكنهم الآن استخدام جميع الألعاب.

### إضافة مستخدم محدد
```
/اضافة_صلاحية @أحمد
```
**النتيجة:** المستخدم "أحمد" يمكنه الآن استخدام جميع الألعاب.

### حذف صلاحية مستخدم
```
/حذف_صلاحية @أحمد
```
**النتيجة:** المستخدم "أحمد" لا يمكنه الآن استخدام الألعاب.

### عرض قائمة الصلاحيات
```
/قائمة_الصلاحيات
```
**النتيجة:** عرض قائمة مفصلة بجميع المستخدمين والأدوار المصرح لهم.

## 🔧 التكوين التقني

### ملف config.json
```json
{
  "allowedRoleIds": [
    "1343402511742009364",
    "1379302045814751272",
    "USER_ID_HERE",
    "ROLE_ID_HERE"
  ]
}
```

### التحديث التلقائي
- عند إضافة أو حذف صلاحية، يتم تحديث:
  1. ملف `config.json` على القرص الصلب
  2. متغير `allowedRoleIds` في الذاكرة
  3. جميع فحوصات الصلاحيات تعمل فوراً

## 🚀 الألعاب المدعومة

### 🎲 روليت (Roulette)
- لعبة البقاء حتى النهاية
- يتنافس اللاعبون في جولات متتالية

### 🪑 كراسي (Musical Chairs)
- لعبة الكراسي الموسيقية
- اضغط بسرعة لتأمين كرسي

### 🕵️ مافيا (Mafia)
- لعبة استراتيجية معقدة
- أدوار متنوعة: مافيا، مواطنين، طبيب، محقق

### 🔎 جاسوس (Spy)
- لعبة اكتشاف الجاسوس
- تخمين المكان والتصويت

## 📝 ملاحظات مهمة

1. **الصلاحيات تطبق على جميع الألعاب:** عند إضافة صلاحية لمستخدم، يمكنه استخدام جميع الألعاب الأربع.

2. **الأدوار مقابل المستخدمين:** يمكن إضافة أدوار كاملة أو مستخدمين فرديين.

3. **الحفظ التلقائي:** جميع التغييرات تحفظ تلقائياً ولا تحتاج إعادة تشغيل.

4. **الأمان:** فقط المشرفين والمستخدمين المصرح لهم يمكنهم إدارة الصلاحيات.

## 🛠️ استكشاف الأخطاء

### مشاكل شائعة وحلولها

**المشكلة:** "ليس لديك الإذن لإدارة الصلاحيات"
**الحل:** تأكد من أن لديك صلاحية Administrator أو أنك مدرج في قائمة allowedRoleIds.

**المشكلة:** "يجب أن تذكر مستخدماً أو رول"
**الحل:** استخدم @ لذكر المستخدم أو الرول بشكل صحيح.

**المشكلة:** "لديه صلاحية بالفعل"
**الحل:** المستخدم أو الرول مضاف بالفعل، لا حاجة لإضافته مرة أخرى.
