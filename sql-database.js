// SQL Database System for Discord Bot
const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

class SQLDatabase {
  constructor() {
    this.dbPath = path.join(__dirname, 'bot_database.db');
    this.backupDir = path.join(__dirname, 'sql_backups');
    this.db = null;
    this.ensureBackupDir();
    this.init();
  }

  // Ensure backup directory exists
  ensureBackupDir() {
    if (!fs.existsSync(this.backupDir)) {
      fs.mkdirSync(this.backupDir, { recursive: true });
      console.log('📁 Created SQL backups directory');
    }
  }

  // Initialize database connection and create tables
  async init() {
    return new Promise((resolve, reject) => {
      this.db = new sqlite3.Database(this.dbPath, (err) => {
        if (err) {
          console.error('❌ Error opening database:', err);
          reject(err);
        } else {
          console.log('✅ Connected to SQLite database');
          this.createTables().then(resolve).catch(reject);
        }
      });
    });
  }

  // Create all necessary tables
  async createTables() {
    const tables = [
      // Users table
      `CREATE TABLE IF NOT EXISTS users (
        user_id TEXT PRIMARY KEY,
        username TEXT,
        points INTEGER DEFAULT 0,
        games_played INTEGER DEFAULT 0,
        wins INTEGER DEFAULT 0,
        losses INTEGER DEFAULT 0,
        win_rate REAL DEFAULT 0.0,
        favorite_game TEXT,
        join_date DATETIME DEFAULT CURRENT_TIMESTAMP,
        last_active DATETIME DEFAULT CURRENT_TIMESTAMP,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Games table
      `CREATE TABLE IF NOT EXISTS games (
        game_id INTEGER PRIMARY KEY AUTOINCREMENT,
        game_type TEXT NOT NULL,
        channel_id TEXT,
        guild_id TEXT,
        host_id TEXT,
        players_count INTEGER DEFAULT 0,
        winner_id TEXT,
        start_time DATETIME DEFAULT CURRENT_TIMESTAMP,
        end_time DATETIME,
        duration INTEGER,
        status TEXT DEFAULT 'active',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Game participants table
      `CREATE TABLE IF NOT EXISTS game_participants (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        game_id INTEGER,
        user_id TEXT,
        position INTEGER,
        eliminated_round INTEGER,
        points_earned INTEGER DEFAULT 0,
        joined_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (game_id) REFERENCES games(game_id),
        FOREIGN KEY (user_id) REFERENCES users(user_id)
      )`,

      // Counting game table
      `CREATE TABLE IF NOT EXISTS counting_game (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        current_count INTEGER DEFAULT 0,
        last_user_id TEXT,
        channel_id TEXT,
        guild_id TEXT,
        last_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Permissions table
      `CREATE TABLE IF NOT EXISTS permissions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        entity_id TEXT NOT NULL,
        entity_type TEXT NOT NULL, -- 'user' or 'role'
        permission_type TEXT NOT NULL, -- 'game_access', 'admin', etc.
        granted_by TEXT,
        granted_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        is_active BOOLEAN DEFAULT 1,
        UNIQUE(entity_id, permission_type)
      )`,

      // Bot settings table
      `CREATE TABLE IF NOT EXISTS bot_settings (
        setting_key TEXT PRIMARY KEY,
        setting_value TEXT,
        setting_type TEXT DEFAULT 'string',
        description TEXT,
        updated_by TEXT,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Audit log table
      `CREATE TABLE IF NOT EXISTS audit_log (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        action TEXT NOT NULL,
        entity_type TEXT,
        entity_id TEXT,
        user_id TEXT,
        old_value TEXT,
        new_value TEXT,
        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
        ip_address TEXT,
        details TEXT
      )`,

      // Game statistics table
      `CREATE TABLE IF NOT EXISTS game_statistics (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        game_type TEXT NOT NULL,
        total_played INTEGER DEFAULT 0,
        total_players INTEGER DEFAULT 0,
        average_duration REAL DEFAULT 0.0,
        most_wins_user_id TEXT,
        last_played DATETIME,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(game_type)
      )`
    ];

    for (const tableSQL of tables) {
      await this.runQuery(tableSQL);
    }

    // Create indexes for better performance
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_users_points ON users(points DESC)',
      'CREATE INDEX IF NOT EXISTS idx_users_wins ON users(wins DESC)',
      'CREATE INDEX IF NOT EXISTS idx_games_type ON games(game_type)',
      'CREATE INDEX IF NOT EXISTS idx_games_winner ON games(winner_id)',
      'CREATE INDEX IF NOT EXISTS idx_games_start_time ON games(start_time)',
      'CREATE INDEX IF NOT EXISTS idx_participants_game ON game_participants(game_id)',
      'CREATE INDEX IF NOT EXISTS idx_participants_user ON game_participants(user_id)',
      'CREATE INDEX IF NOT EXISTS idx_permissions_entity ON permissions(entity_id)',
      'CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)',
      'CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(user_id)'
    ];

    for (const indexSQL of indexes) {
      await this.runQuery(indexSQL);
    }

    console.log('✅ All database tables and indexes created successfully');
    
    // Initialize default settings
    await this.initializeDefaultSettings();
  }

  // Initialize default bot settings
  async initializeDefaultSettings() {
    const defaultSettings = [
      ['counting_channel_id', '1386926777120850053', 'string', 'Channel ID for counting game'],
      ['auto_backup_enabled', 'true', 'boolean', 'Enable automatic backups'],
      ['backup_interval_minutes', '5', 'number', 'Backup interval in minutes'],
      ['max_backups_keep', '50', 'number', 'Maximum number of backups to keep'],
      ['maintenance_mode', 'false', 'boolean', 'Bot maintenance mode'],
      ['bot_prefix', '+', 'string', 'Bot command prefix'],
      ['points_per_win', '1', 'number', 'Points awarded for winning a game'],
      ['ability_cost', '3', 'number', 'Cost in points for using abilities']
    ];

    for (const [key, value, type, description] of defaultSettings) {
      await this.setSetting(key, value, type, description, 'system');
    }
  }

  // Helper method to run SQL queries
  runQuery(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.run(sql, params, function(err) {
        if (err) {
          console.error('❌ SQL Error:', err);
          reject(err);
        } else {
          resolve({ lastID: this.lastID, changes: this.changes });
        }
      });
    });
  }

  // Helper method to get single row
  getRow(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.get(sql, params, (err, row) => {
        if (err) {
          console.error('❌ SQL Error:', err);
          reject(err);
        } else {
          resolve(row);
        }
      });
    });
  }

  // Helper method to get all rows
  getAllRows(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.all(sql, params, (err, rows) => {
        if (err) {
          console.error('❌ SQL Error:', err);
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  // User Management Methods
  async createUser(userId, username = null) {
    try {
      const sql = `
        INSERT OR IGNORE INTO users (user_id, username, join_date, last_active)
        VALUES (?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      `;
      await this.runQuery(sql, [userId, username]);
      await this.logAction('user_created', 'user', userId, userId);
      return true;
    } catch (error) {
      console.error('Error creating user:', error);
      return false;
    }
  }

  async getUserPoints(userId) {
    try {
      await this.createUser(userId); // Ensure user exists
      const row = await this.getRow('SELECT points FROM users WHERE user_id = ?', [userId]);
      return row ? row.points : 0;
    } catch (error) {
      console.error('Error getting user points:', error);
      return 0;
    }
  }

  async updateUserPoints(userId, points, operation = 'set') {
    try {
      await this.createUser(userId);
      
      let sql;
      if (operation === 'add') {
        sql = 'UPDATE users SET points = points + ?, updated_at = CURRENT_TIMESTAMP WHERE user_id = ?';
      } else if (operation === 'subtract') {
        sql = 'UPDATE users SET points = MAX(0, points - ?), updated_at = CURRENT_TIMESTAMP WHERE user_id = ?';
      } else {
        sql = 'UPDATE users SET points = ?, updated_at = CURRENT_TIMESTAMP WHERE user_id = ?';
      }
      
      const result = await this.runQuery(sql, [points, userId]);
      await this.logAction('points_updated', 'user', userId, userId, null, points.toString());
      
      return await this.getUserPoints(userId);
    } catch (error) {
      console.error('Error updating user points:', error);
      return 0;
    }
  }

  async awardWin(userId, gameType = 'unknown') {
    try {
      await this.createUser(userId);
      
      const pointsPerWin = await this.getSetting('points_per_win', 1);
      
      const sql = `
        UPDATE users SET 
          points = points + ?,
          wins = wins + 1,
          games_played = games_played + 1,
          win_rate = ROUND((wins + 1.0) / (games_played + 1.0) * 100, 2),
          favorite_game = ?,
          last_active = CURRENT_TIMESTAMP,
          updated_at = CURRENT_TIMESTAMP
        WHERE user_id = ?
      `;
      
      await this.runQuery(sql, [pointsPerWin, gameType, userId]);
      await this.logAction('game_won', 'user', userId, userId, null, gameType);
      
      // Update game statistics
      await this.updateGameStatistics(gameType);
      
      return await this.getUserPoints(userId);
    } catch (error) {
      console.error('Error awarding win:', error);
      return 0;
    }
  }

  async recordLoss(userId, gameType = 'unknown') {
    try {
      await this.createUser(userId);
      
      const sql = `
        UPDATE users SET 
          losses = losses + 1,
          games_played = games_played + 1,
          win_rate = ROUND(wins / (games_played + 1.0) * 100, 2),
          last_active = CURRENT_TIMESTAMP,
          updated_at = CURRENT_TIMESTAMP
        WHERE user_id = ?
      `;
      
      await this.runQuery(sql, [userId]);
      await this.logAction('game_lost', 'user', userId, userId, null, gameType);
      
      return true;
    } catch (error) {
      console.error('Error recording loss:', error);
      return false;
    }
  }

  // Get leaderboard
  async getLeaderboard(limit = 10, orderBy = 'points') {
    try {
      const validOrderBy = ['points', 'wins', 'win_rate', 'games_played'];
      if (!validOrderBy.includes(orderBy)) {
        orderBy = 'points';
      }
      
      const sql = `
        SELECT user_id, username, points, wins, losses, games_played, win_rate, favorite_game
        FROM users 
        WHERE games_played > 0
        ORDER BY ${orderBy} DESC, points DESC
        LIMIT ?
      `;
      
      return await this.getAllRows(sql, [limit]);
    } catch (error) {
      console.error('Error getting leaderboard:', error);
      return [];
    }
  }

  // Counting Game Methods
  async getCurrentCount() {
    try {
      const row = await this.getRow('SELECT current_count FROM counting_game ORDER BY id DESC LIMIT 1');
      return row ? row.current_count : 0;
    } catch (error) {
      console.error('Error getting current count:', error);
      return 0;
    }
  }

  async updateCount(newCount, userId, channelId, guildId) {
    try {
      const sql = `
        INSERT INTO counting_game (current_count, last_user_id, channel_id, guild_id, last_updated)
        VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)
      `;
      
      await this.runQuery(sql, [newCount, userId, channelId, guildId]);
      await this.logAction('count_updated', 'counting', newCount.toString(), userId, null, newCount.toString());
      
      return true;
    } catch (error) {
      console.error('Error updating count:', error);
      return false;
    }
  }

  async resetCount(userId) {
    try {
      await this.updateCount(0, userId, null, null);
      await this.logAction('count_reset', 'counting', '0', userId);
      return true;
    } catch (error) {
      console.error('Error resetting count:', error);
      return false;
    }
  }

  // Permission Management
  async grantPermission(entityId, entityType, permissionType, grantedBy) {
    try {
      const sql = `
        INSERT OR REPLACE INTO permissions (entity_id, entity_type, permission_type, granted_by, granted_at, is_active)
        VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP, 1)
      `;
      
      await this.runQuery(sql, [entityId, entityType, permissionType, grantedBy]);
      await this.logAction('permission_granted', 'permission', entityId, grantedBy, null, `${entityType}:${permissionType}`);
      
      return true;
    } catch (error) {
      console.error('Error granting permission:', error);
      return false;
    }
  }

  async revokePermission(entityId, permissionType, revokedBy) {
    try {
      const sql = `
        UPDATE permissions 
        SET is_active = 0, updated_at = CURRENT_TIMESTAMP
        WHERE entity_id = ? AND permission_type = ?
      `;
      
      await this.runQuery(sql, [entityId, permissionType]);
      await this.logAction('permission_revoked', 'permission', entityId, revokedBy, null, permissionType);
      
      return true;
    } catch (error) {
      console.error('Error revoking permission:', error);
      return false;
    }
  }

  async hasPermission(entityId, permissionType) {
    try {
      const row = await this.getRow(
        'SELECT id FROM permissions WHERE entity_id = ? AND permission_type = ? AND is_active = 1',
        [entityId, permissionType]
      );
      return !!row;
    } catch (error) {
      console.error('Error checking permission:', error);
      return false;
    }
  }

  async getAllPermissions() {
    try {
      return await this.getAllRows(`
        SELECT entity_id, entity_type, permission_type, granted_by, granted_at
        FROM permissions 
        WHERE is_active = 1
        ORDER BY granted_at DESC
      `);
    } catch (error) {
      console.error('Error getting permissions:', error);
      return [];
    }
  }

  // Settings Management
  async setSetting(key, value, type = 'string', description = null, updatedBy = null) {
    try {
      const sql = `
        INSERT OR REPLACE INTO bot_settings (setting_key, setting_value, setting_type, description, updated_by, updated_at)
        VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
      `;
      
      await this.runQuery(sql, [key, value, type, description, updatedBy]);
      return true;
    } catch (error) {
      console.error('Error setting value:', error);
      return false;
    }
  }

  async getSetting(key, defaultValue = null) {
    try {
      const row = await this.getRow('SELECT setting_value, setting_type FROM bot_settings WHERE setting_key = ?', [key]);
      
      if (!row) return defaultValue;
      
      const { setting_value, setting_type } = row;
      
      switch (setting_type) {
        case 'number':
          return parseFloat(setting_value);
        case 'boolean':
          return setting_value === 'true';
        case 'json':
          return JSON.parse(setting_value);
        default:
          return setting_value;
      }
    } catch (error) {
      console.error('Error getting setting:', error);
      return defaultValue;
    }
  }

  // Game Statistics
  async updateGameStatistics(gameType) {
    try {
      const sql = `
        INSERT OR REPLACE INTO game_statistics (game_type, total_played, last_played, updated_at)
        VALUES (?, 
          COALESCE((SELECT total_played FROM game_statistics WHERE game_type = ?), 0) + 1,
          CURRENT_TIMESTAMP,
          CURRENT_TIMESTAMP
        )
      `;
      
      await this.runQuery(sql, [gameType, gameType]);
      return true;
    } catch (error) {
      console.error('Error updating game statistics:', error);
      return false;
    }
  }

  // Audit Logging
  async logAction(action, entityType, entityId, userId, oldValue = null, newValue = null, details = null) {
    try {
      const sql = `
        INSERT INTO audit_log (action, entity_type, entity_id, user_id, old_value, new_value, details, timestamp)
        VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
      `;
      
      await this.runQuery(sql, [action, entityType, entityId, userId, oldValue, newValue, details]);
      return true;
    } catch (error) {
      console.error('Error logging action:', error);
      return false;
    }
  }

  // Database Backup
  async createBackup() {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupPath = path.join(this.backupDir, `backup_${timestamp}.db`);
      
      return new Promise((resolve, reject) => {
        const backup = this.db.backup(backupPath);
        backup.step(-1, (err) => {
          if (err) {
            reject(err);
          } else {
            backup.finish((err) => {
              if (err) {
                reject(err);
              } else {
                console.log(`💾 SQL Backup created: ${backupPath}`);
                resolve(backupPath);
              }
            });
          }
        });
      });
    } catch (error) {
      console.error('Error creating SQL backup:', error);
      throw error;
    }
  }

  // Close database connection
  async close() {
    return new Promise((resolve) => {
      if (this.db) {
        this.db.close((err) => {
          if (err) {
            console.error('Error closing database:', err);
          } else {
            console.log('✅ Database connection closed');
          }
          resolve();
        });
      } else {
        resolve();
      }
    });
  }
}

// Create singleton instance
const sqlDatabase = new SQLDatabase();

module.exports = {
  sqlDatabase
};
