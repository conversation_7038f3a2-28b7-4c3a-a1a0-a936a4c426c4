// SQL-based Points System for Discord Bot Games
const { sqlDatabase } = require('./sql-database.js');

class SQLPointsManager {
  constructor() {
    this.initialized = false;
    this.init();
  }

  async init() {
    try {
      // Wait for database to be ready
      await new Promise(resolve => {
        const checkDB = () => {
          if (sqlDatabase.db) {
            resolve();
          } else {
            setTimeout(checkDB, 100);
          }
        };
        checkDB();
      });
      
      this.initialized = true;
      console.log('✅ SQL Points Manager initialized');
    } catch (error) {
      console.error('❌ Error initializing SQL Points Manager:', error);
    }
  }

  async ensureInitialized() {
    while (!this.initialized) {
      await new Promise(resolve => setTimeout(resolve, 10));
    }
  }

  // Get user points
  async getPoints(userId) {
    await this.ensureInitialized();
    return await sqlDatabase.getUserPoints(userId);
  }

  // Get user stats
  async getStats(userId) {
    await this.ensureInitialized();
    try {
      const row = await sqlDatabase.getRow(`
        SELECT games_played, wins, losses, win_rate, favorite_game, join_date, last_active
        FROM users WHERE user_id = ?
      `, [userId]);

      if (!row) {
        await sqlDatabase.createUser(userId);
        return {
          gamesPlayed: 0,
          wins: 0,
          losses: 0,
          winRate: 0.0,
          favoriteGame: null,
          joinDate: new Date().toISOString(),
          lastActive: new Date().toISOString()
        };
      }

      return {
        gamesPlayed: row.games_played,
        wins: row.wins,
        losses: row.losses,
        winRate: row.win_rate,
        favoriteGame: row.favorite_game,
        joinDate: row.join_date,
        lastActive: row.last_active
      };
    } catch (error) {
      console.error('Error getting user stats:', error);
      return {
        gamesPlayed: 0,
        wins: 0,
        losses: 0,
        winRate: 0.0,
        favoriteGame: null,
        joinDate: new Date().toISOString(),
        lastActive: new Date().toISOString()
      };
    }
  }

  // Add points to user
  async addPoints(userId, amount) {
    await this.ensureInitialized();
    return await sqlDatabase.updateUserPoints(userId, amount, 'add');
  }

  // Remove points from user
  async removePoints(userId, amount) {
    await this.ensureInitialized();
    return await sqlDatabase.updateUserPoints(userId, amount, 'subtract');
  }

  // Set points for user (admin function)
  async setPoints(userId, amount) {
    await this.ensureInitialized();
    return await sqlDatabase.updateUserPoints(userId, amount, 'set');
  }

  // Check if user has enough points
  async hasEnoughPoints(userId, amount) {
    const points = await this.getPoints(userId);
    return points >= amount;
  }

  // Award points for winning a game
  async awardWin(userId, gameType = 'unknown') {
    await this.ensureInitialized();
    return await sqlDatabase.awardWin(userId, gameType);
  }

  // Record a loss
  async recordLoss(userId, gameType = 'unknown') {
    await this.ensureInitialized();
    await sqlDatabase.recordLoss(userId, gameType);
  }

  // Use ability (deduct points)
  async useAbility(userId) {
    await this.ensureInitialized();
    const abilityCost = await sqlDatabase.getSetting('ability_cost', 3);
    
    if (!(await this.hasEnoughPoints(userId, abilityCost))) {
      return false;
    }
    
    await this.removePoints(userId, abilityCost);
    await sqlDatabase.logAction('ability_used', 'user', userId, userId, null, abilityCost.toString());
    return true;
  }

  // Get leaderboard
  getLeaderboard(limit = 10, orderBy = 'points') {
    return sqlDatabase.getLeaderboard(limit, orderBy);
  }

  // Get all user data (for compatibility with old system)
  async getAllData() {
    await this.ensureInitialized();
    try {
      const users = await sqlDatabase.getAllRows(`
        SELECT user_id, points, games_played, wins, losses, win_rate, favorite_game
        FROM users
      `);

      const data = {};
      users.forEach(user => {
        data[user.user_id] = {
          points: user.points,
          stats: {
            gamesPlayed: user.games_played,
            wins: user.wins,
            losses: user.losses,
            winRate: user.win_rate,
            favoriteGame: user.favorite_game
          }
        };
      });

      return data;
    } catch (error) {
      console.error('Error getting all data:', error);
      return {};
    }
  }

  // Get user rank
  async getUserRank(userId) {
    await this.ensureInitialized();
    try {
      const row = await sqlDatabase.getRow(`
        SELECT COUNT(*) + 1 as rank
        FROM users
        WHERE points > (SELECT points FROM users WHERE user_id = ?)
      `, [userId]);

      return row ? row.rank : null;
    } catch (error) {
      console.error('Error getting user rank:', error);
      return null;
    }
  }

  // Get total users count
  async getTotalUsers() {
    await this.ensureInitialized();
    try {
      const row = await sqlDatabase.getRow('SELECT COUNT(*) as total FROM users WHERE games_played > 0');
      return row ? row.total : 0;
    } catch (error) {
      console.error('Error getting total users:', error);
      return 0;
    }
  }

  // Get user detailed stats
  async getDetailedStats(userId) {
    await this.ensureInitialized();
    try {
      const userStats = await this.getStats(userId);
      const userRank = await this.getUserRank(userId);
      const totalUsers = await this.getTotalUsers();
      const points = await this.getPoints(userId);

      // Get recent games
      const recentGames = await sqlDatabase.getAllRows(`
        SELECT g.game_type, g.start_time, g.end_time, g.winner_id, gp.points_earned
        FROM game_participants gp
        JOIN games g ON gp.game_id = g.game_id
        WHERE gp.user_id = ?
        ORDER BY g.start_time DESC
        LIMIT 10
      `, [userId]);

      // Get favorite game stats
      let favoriteGameStats = null;
      if (userStats.favoriteGame) {
        const gameStats = await sqlDatabase.getRow(`
          SELECT COUNT(*) as played, SUM(CASE WHEN g.winner_id = ? THEN 1 ELSE 0 END) as won
          FROM game_participants gp
          JOIN games g ON gp.game_id = g.game_id
          WHERE gp.user_id = ? AND g.game_type = ?
        `, [userId, userId, userStats.favoriteGame]);

        favoriteGameStats = {
          gameType: userStats.favoriteGame,
          played: gameStats ? gameStats.played : 0,
          won: gameStats ? gameStats.won : 0,
          winRate: gameStats && gameStats.played > 0 ? 
            Math.round((gameStats.won / gameStats.played) * 100) : 0
        };
      }

      return {
        ...userStats,
        points,
        rank: userRank,
        totalUsers,
        recentGames,
        favoriteGameStats
      };
    } catch (error) {
      console.error('Error getting detailed stats:', error);
      return null;
    }
  }

  // Migrate data from JSON to SQL
  async migrateFromJSON(jsonData) {
    await this.ensureInitialized();
    try {
      console.log('🔄 Starting migration from JSON to SQL...');
      let migratedUsers = 0;

      for (const [userId, userData] of Object.entries(jsonData)) {
        try {
          // Create user
          await sqlDatabase.createUser(userId);

          // Update user data
          await sqlDatabase.runQuery(`
            UPDATE users SET
              points = ?,
              games_played = ?,
              wins = ?,
              losses = ?,
              win_rate = ?,
              updated_at = CURRENT_TIMESTAMP
            WHERE user_id = ?
          `, [
            userData.points || 0,
            userData.stats?.gamesPlayed || 0,
            userData.stats?.wins || 0,
            userData.stats?.losses || 0,
            userData.stats?.winRate || 0.0,
            userId
          ]);

          migratedUsers++;
        } catch (error) {
          console.error(`Error migrating user ${userId}:`, error);
        }
      }

      console.log(`✅ Migration completed: ${migratedUsers} users migrated`);
      await sqlDatabase.logAction('data_migration', 'system', 'json_to_sql', 'system', null, migratedUsers.toString());
      
      return migratedUsers;
    } catch (error) {
      console.error('Error during migration:', error);
      throw error;
    }
  }

  // Export data to JSON (for backup compatibility)
  async exportToJSON() {
    await this.ensureInitialized();
    return await this.getAllData();
  }

  // Get game statistics
  async getGameStatistics() {
    await this.ensureInitialized();
    try {
      return await sqlDatabase.getAllRows(`
        SELECT game_type, total_played, total_players, average_duration, most_wins_user_id, last_played
        FROM game_statistics
        ORDER BY total_played DESC
      `);
    } catch (error) {
      console.error('Error getting game statistics:', error);
      return [];
    }
  }

  // Get top players by different criteria
  async getTopPlayers(criteria = 'points', limit = 10) {
    await this.ensureInitialized();
    const validCriteria = ['points', 'wins', 'win_rate', 'games_played'];
    if (!validCriteria.includes(criteria)) {
      criteria = 'points';
    }

    try {
      return await sqlDatabase.getAllRows(`
        SELECT user_id, username, points, wins, losses, games_played, win_rate, favorite_game
        FROM users
        WHERE games_played > 0
        ORDER BY ${criteria} DESC, points DESC
        LIMIT ?
      `, [limit]);
    } catch (error) {
      console.error('Error getting top players:', error);
      return [];
    }
  }

  // Search users
  async searchUsers(query, limit = 10) {
    await this.ensureInitialized();
    try {
      return await sqlDatabase.getAllRows(`
        SELECT user_id, username, points, wins, games_played
        FROM users
        WHERE username LIKE ? OR user_id LIKE ?
        ORDER BY points DESC
        LIMIT ?
      `, [`%${query}%`, `%${query}%`, limit]);
    } catch (error) {
      console.error('Error searching users:', error);
      return [];
    }
  }
}

// Constants for compatibility
const ABILITY_COST = 3;
const WIN_REWARD = 1;

// Create singleton instance
const sqlPointsManager = new SQLPointsManager();

module.exports = {
  pointsManager: sqlPointsManager,
  sqlPointsManager,
  ABILITY_COST,
  WIN_REWARD
};
