// Comprehensive Database Backup System for Discord Bot
const fs = require('fs').promises;
const fsSync = require('fs');
const path = require('path');

class DatabaseManager {
  constructor() {
    this.backupDir = path.join(__dirname, 'backups');
    this.dataDir = path.join(__dirname, 'data');
    this.ensureDirectories();
    
    // Data file paths
    this.files = {
      points: path.join(__dirname, 'points.json'),
      counting: path.join(__dirname, 'counting_data.json'),
      config: path.join(__dirname, 'config.json'),
      permissions: path.join(__dirname, 'config.json'), // Same as config for allowedRoleIds
      gameStats: path.join(this.dataDir, 'game_stats.json'),
      userProfiles: path.join(this.dataDir, 'user_profiles.json'),
      serverSettings: path.join(this.dataDir, 'server_settings.json')
    };

    // Initialize data files if they don't exist
    this.initializeDataFiles();
    
    // Auto-backup every 5 minutes
    this.startAutoBackup();
  }

  // Ensure backup and data directories exist
  async ensureDirectories() {
    try {
      if (!fsSync.existsSync(this.backupDir)) {
        await fs.mkdir(this.backupDir, { recursive: true });
        console.log('📁 Created backups directory');
      }
      if (!fsSync.existsSync(this.dataDir)) {
        await fs.mkdir(this.dataDir, { recursive: true });
        console.log('📁 Created data directory');
      }
    } catch (error) {
      console.error('❌ Error creating directories:', error);
    }
  }

  // Initialize data files with default structures
  async initializeDataFiles() {
    try {
      // Initialize game stats file
      if (!fsSync.existsSync(this.files.gameStats)) {
        const defaultGameStats = {
          totalGamesPlayed: 0,
          gameTypes: {
            roulette: { played: 0, winners: [] },
            chairs: { played: 0, winners: [] },
            mafia: { played: 0, winners: [] },
            spy: { played: 0, winners: [] },
            quiz: { played: 0, winners: [] },
            button: { played: 0, winners: [] }
          },
          lastUpdated: new Date().toISOString()
        };
        await fs.writeFile(this.files.gameStats, JSON.stringify(defaultGameStats, null, 2));
        console.log('📊 Initialized game stats file');
      }

      // Initialize user profiles file
      if (!fsSync.existsSync(this.files.userProfiles)) {
        const defaultUserProfiles = {
          users: {},
          lastUpdated: new Date().toISOString()
        };
        await fs.writeFile(this.files.userProfiles, JSON.stringify(defaultUserProfiles, null, 2));
        console.log('👥 Initialized user profiles file');
      }

      // Initialize server settings file
      if (!fsSync.existsSync(this.files.serverSettings)) {
        const defaultServerSettings = {
          countingChannelId: "1386926777120850053",
          maintenanceMode: false,
          announcements: [],
          botSettings: {
            autoBackup: true,
            backupInterval: 300000, // 5 minutes
            maxBackups: 50
          },
          lastUpdated: new Date().toISOString()
        };
        await fs.writeFile(this.files.serverSettings, JSON.stringify(defaultServerSettings, null, 2));
        console.log('⚙️ Initialized server settings file');
      }

    } catch (error) {
      console.error('❌ Error initializing data files:', error);
    }
  }

  // Create a complete backup of all data
  async createFullBackup() {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupFileName = `full_backup_${timestamp}.json`;
      const backupPath = path.join(this.backupDir, backupFileName);

      const backupData = {
        timestamp: new Date().toISOString(),
        version: "1.0.0",
        data: {}
      };

      // Backup all data files
      for (const [key, filePath] of Object.entries(this.files)) {
        try {
          if (fsSync.existsSync(filePath)) {
            const fileContent = await fs.readFile(filePath, 'utf8');
            backupData.data[key] = JSON.parse(fileContent);
          } else {
            backupData.data[key] = null;
            console.log(`⚠️ File not found: ${filePath}`);
          }
        } catch (error) {
          console.error(`❌ Error backing up ${key}:`, error);
          backupData.data[key] = null;
        }
      }

      // Save backup
      await fs.writeFile(backupPath, JSON.stringify(backupData, null, 2));
      console.log(`💾 Full backup created: ${backupFileName}`);

      // Clean old backups
      await this.cleanOldBackups();

      return backupPath;
    } catch (error) {
      console.error('❌ Error creating full backup:', error);
      throw error;
    }
  }

  // Restore from backup
  async restoreFromBackup(backupFileName) {
    try {
      const backupPath = path.join(this.backupDir, backupFileName);
      
      if (!fsSync.existsSync(backupPath)) {
        throw new Error(`Backup file not found: ${backupFileName}`);
      }

      const backupContent = await fs.readFile(backupPath, 'utf8');
      const backupData = JSON.parse(backupContent);

      console.log(`🔄 Restoring from backup: ${backupFileName}`);
      console.log(`📅 Backup date: ${backupData.timestamp}`);

      // Restore all data files
      for (const [key, data] of Object.entries(backupData.data)) {
        if (data && this.files[key]) {
          try {
            await fs.writeFile(this.files[key], JSON.stringify(data, null, 2));
            console.log(`✅ Restored ${key}`);
          } catch (error) {
            console.error(`❌ Error restoring ${key}:`, error);
          }
        }
      }

      console.log('🎉 Backup restoration completed!');
      return true;
    } catch (error) {
      console.error('❌ Error restoring backup:', error);
      throw error;
    }
  }

  // Get list of available backups
  async getBackupList() {
    try {
      const files = await fs.readdir(this.backupDir);
      const backups = files
        .filter(file => file.startsWith('full_backup_') && file.endsWith('.json'))
        .map(file => {
          const stats = fsSync.statSync(path.join(this.backupDir, file));
          return {
            filename: file,
            size: stats.size,
            created: stats.birthtime,
            modified: stats.mtime
          };
        })
        .sort((a, b) => b.created - a.created);

      return backups;
    } catch (error) {
      console.error('❌ Error getting backup list:', error);
      return [];
    }
  }

  // Clean old backups (keep only the latest 50)
  async cleanOldBackups() {
    try {
      const backups = await this.getBackupList();
      const maxBackups = 50;

      if (backups.length > maxBackups) {
        const backupsToDelete = backups.slice(maxBackups);
        
        for (const backup of backupsToDelete) {
          const backupPath = path.join(this.backupDir, backup.filename);
          await fs.unlink(backupPath);
          console.log(`🗑️ Deleted old backup: ${backup.filename}`);
        }
      }
    } catch (error) {
      console.error('❌ Error cleaning old backups:', error);
    }
  }

  // Update game statistics
  async updateGameStats(gameType, winnerId = null) {
    try {
      const statsData = await this.loadData('gameStats');
      
      statsData.totalGamesPlayed++;
      if (statsData.gameTypes[gameType]) {
        statsData.gameTypes[gameType].played++;
        if (winnerId) {
          statsData.gameTypes[gameType].winners.push({
            userId: winnerId,
            timestamp: new Date().toISOString()
          });
        }
      }
      statsData.lastUpdated = new Date().toISOString();

      await this.saveData('gameStats', statsData);
      console.log(`📊 Updated game stats for ${gameType}`);
    } catch (error) {
      console.error('❌ Error updating game stats:', error);
    }
  }

  // Update user profile
  async updateUserProfile(userId, updates) {
    try {
      const profilesData = await this.loadData('userProfiles');
      
      if (!profilesData.users[userId]) {
        profilesData.users[userId] = {
          userId: userId,
          joinDate: new Date().toISOString(),
          totalGamesPlayed: 0,
          totalWins: 0,
          favoriteGame: null,
          lastActive: new Date().toISOString()
        };
      }

      // Apply updates
      Object.assign(profilesData.users[userId], updates);
      profilesData.users[userId].lastActive = new Date().toISOString();
      profilesData.lastUpdated = new Date().toISOString();

      await this.saveData('userProfiles', profilesData);
    } catch (error) {
      console.error('❌ Error updating user profile:', error);
    }
  }

  // Load data from a specific file
  async loadData(dataType) {
    try {
      if (!this.files[dataType]) {
        throw new Error(`Unknown data type: ${dataType}`);
      }

      if (fsSync.existsSync(this.files[dataType])) {
        const content = await fs.readFile(this.files[dataType], 'utf8');
        return JSON.parse(content);
      } else {
        // Return default structure based on data type
        return this.getDefaultData(dataType);
      }
    } catch (error) {
      console.error(`❌ Error loading ${dataType}:`, error);
      return this.getDefaultData(dataType);
    }
  }

  // Save data to a specific file
  async saveData(dataType, data) {
    try {
      if (!this.files[dataType]) {
        throw new Error(`Unknown data type: ${dataType}`);
      }

      await fs.writeFile(this.files[dataType], JSON.stringify(data, null, 2));
    } catch (error) {
      console.error(`❌ Error saving ${dataType}:`, error);
      throw error;
    }
  }

  // Get default data structure for a data type
  getDefaultData(dataType) {
    const defaults = {
      gameStats: {
        totalGamesPlayed: 0,
        gameTypes: {
          roulette: { played: 0, winners: [] },
          chairs: { played: 0, winners: [] },
          mafia: { played: 0, winners: [] },
          spy: { played: 0, winners: [] },
          quiz: { played: 0, winners: [] },
          button: { played: 0, winners: [] }
        },
        lastUpdated: new Date().toISOString()
      },
      userProfiles: {
        users: {},
        lastUpdated: new Date().toISOString()
      },
      serverSettings: {
        countingChannelId: "1386926777120850053",
        maintenanceMode: false,
        announcements: [],
        botSettings: {
          autoBackup: true,
          backupInterval: 300000,
          maxBackups: 50
        },
        lastUpdated: new Date().toISOString()
      }
    };

    return defaults[dataType] || {};
  }

  // Start automatic backup system
  startAutoBackup() {
    // Create backup every 5 minutes
    setInterval(async () => {
      try {
        await this.createFullBackup();
        console.log('⏰ Automatic backup completed');
      } catch (error) {
        console.error('❌ Automatic backup failed:', error);
      }
    }, 300000); // 5 minutes

    console.log('🔄 Auto-backup system started (every 5 minutes)');
  }

  // Emergency backup (called before shutdown)
  async emergencyBackup() {
    try {
      console.log('🚨 Creating emergency backup...');
      const backupPath = await this.createFullBackup();
      console.log(`🚨 Emergency backup created: ${backupPath}`);
      return backupPath;
    } catch (error) {
      console.error('❌ Emergency backup failed:', error);
      throw error;
    }
  }
}

// Create singleton instance
const databaseManager = new DatabaseManager();

module.exports = {
  databaseManager
};
