// PostgreSQL Database System for Railway Hosting
const { Pool } = require('pg');

class PostgreSQLDatabase {
  constructor() {
    this.pool = null;
    this.init();
  }

  // Initialize PostgreSQL connection
  async init() {
    try {
      // Railway automatically provides DATABASE_URL
      this.pool = new Pool({
        connectionString: process.env.DATABASE_URL,
        ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
      });

      console.log('✅ Connected to PostgreSQL database');
      await this.createTables();
    } catch (error) {
      console.error('❌ Error connecting to PostgreSQL:', error);
      throw error;
    }
  }

  // Create all necessary tables
  async createTables() {
    const tables = [
      // Users table
      `CREATE TABLE IF NOT EXISTS users (
        user_id TEXT PRIMARY KEY,
        username TEXT,
        points INTEGER DEFAULT 0,
        games_played INTEGER DEFAULT 0,
        wins INTEGER DEFAULT 0,
        losses INTEGER DEFAULT 0,
        win_rate REAL DEFAULT 0.0,
        favorite_game TEXT,
        join_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        last_active TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )`,

      // Games table
      `CREATE TABLE IF NOT EXISTS games (
        game_id SERIAL PRIMARY KEY,
        game_type TEXT NOT NULL,
        channel_id TEXT,
        guild_id TEXT,
        host_id TEXT,
        players_count INTEGER DEFAULT 0,
        winner_id TEXT,
        start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        end_time TIMESTAMP,
        duration INTEGER,
        status TEXT DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )`,

      // Counting game table
      `CREATE TABLE IF NOT EXISTS counting_game (
        id SERIAL PRIMARY KEY,
        current_count INTEGER DEFAULT 0,
        last_user_id TEXT,
        channel_id TEXT,
        guild_id TEXT,
        last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )`,

      // Permissions table
      `CREATE TABLE IF NOT EXISTS permissions (
        id SERIAL PRIMARY KEY,
        entity_id TEXT NOT NULL,
        entity_type TEXT NOT NULL,
        permission_type TEXT NOT NULL,
        granted_by TEXT,
        granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        is_active BOOLEAN DEFAULT true,
        UNIQUE(entity_id, permission_type)
      )`,

      // Bot settings table
      `CREATE TABLE IF NOT EXISTS bot_settings (
        setting_key TEXT PRIMARY KEY,
        setting_value TEXT,
        setting_type TEXT DEFAULT 'string',
        description TEXT,
        updated_by TEXT,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )`,

      // Audit log table
      `CREATE TABLE IF NOT EXISTS audit_log (
        id SERIAL PRIMARY KEY,
        action TEXT NOT NULL,
        entity_type TEXT,
        entity_id TEXT,
        user_id TEXT,
        old_value TEXT,
        new_value TEXT,
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        details TEXT
      )`
    ];

    for (const tableSQL of tables) {
      await this.query(tableSQL);
    }

    // Create indexes
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_users_points ON users(points DESC)',
      'CREATE INDEX IF NOT EXISTS idx_users_wins ON users(wins DESC)',
      'CREATE INDEX IF NOT EXISTS idx_games_type ON games(game_type)',
      'CREATE INDEX IF NOT EXISTS idx_counting_updated ON counting_game(last_updated DESC)',
      'CREATE INDEX IF NOT EXISTS idx_permissions_entity ON permissions(entity_id)',
      'CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp DESC)'
    ];

    for (const indexSQL of indexes) {
      try {
        await this.query(indexSQL);
      } catch (error) {
        // Ignore "already exists" errors
        if (!error.message.includes('already exists')) {
          console.error('Error creating index:', error);
        }
      }
    }

    console.log('✅ All PostgreSQL tables and indexes created successfully');
    await this.initializeDefaultSettings();
  }

  // Initialize default settings
  async initializeDefaultSettings() {
    const defaultSettings = [
      ['counting_channel_id', '1386926777120850053', 'string', 'Channel ID for counting game'],
      ['bot_prefix', '+', 'string', 'Bot command prefix'],
      ['points_per_win', '1', 'number', 'Points awarded for winning a game'],
      ['ability_cost', '3', 'number', 'Cost in points for using abilities']
    ];

    for (const [key, value, type, description] of defaultSettings) {
      await this.setSetting(key, value, type, description, 'system');
    }
  }

  // Execute query
  async query(text, params = []) {
    try {
      const result = await this.pool.query(text, params);
      return result;
    } catch (error) {
      console.error('❌ PostgreSQL Error:', error);
      throw error;
    }
  }

  // User Management
  async createUser(userId, username = null) {
    try {
      const sql = `
        INSERT INTO users (user_id, username, join_date, last_active)
        VALUES ($1, $2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        ON CONFLICT (user_id) DO NOTHING
      `;
      await this.query(sql, [userId, username]);
      return true;
    } catch (error) {
      console.error('Error creating user:', error);
      return false;
    }
  }

  async getUserPoints(userId) {
    try {
      await this.createUser(userId);
      const result = await this.query('SELECT points FROM users WHERE user_id = $1', [userId]);
      return result.rows[0] ? result.rows[0].points : 0;
    } catch (error) {
      console.error('Error getting user points:', error);
      return 0;
    }
  }

  async updateUserPoints(userId, points, operation = 'set') {
    try {
      await this.createUser(userId);
      
      let sql;
      if (operation === 'add') {
        sql = 'UPDATE users SET points = points + $1, updated_at = CURRENT_TIMESTAMP WHERE user_id = $2';
      } else if (operation === 'subtract') {
        sql = 'UPDATE users SET points = GREATEST(0, points - $1), updated_at = CURRENT_TIMESTAMP WHERE user_id = $2';
      } else {
        sql = 'UPDATE users SET points = $1, updated_at = CURRENT_TIMESTAMP WHERE user_id = $2';
      }
      
      await this.query(sql, [points, userId]);
      return await this.getUserPoints(userId);
    } catch (error) {
      console.error('Error updating user points:', error);
      return 0;
    }
  }

  async awardWin(userId, gameType = 'unknown') {
    try {
      await this.createUser(userId);
      
      const pointsPerWin = await this.getSetting('points_per_win', 1);
      
      const sql = `
        UPDATE users SET 
          points = points + $1,
          wins = wins + 1,
          games_played = games_played + 1,
          win_rate = ROUND((wins + 1.0) / (games_played + 1.0) * 100, 2),
          favorite_game = $2,
          last_active = CURRENT_TIMESTAMP,
          updated_at = CURRENT_TIMESTAMP
        WHERE user_id = $3
      `;
      
      await this.query(sql, [pointsPerWin, gameType, userId]);
      return await this.getUserPoints(userId);
    } catch (error) {
      console.error('Error awarding win:', error);
      return 0;
    }
  }

  // Counting Game Methods
  async getCurrentCount() {
    try {
      const result = await this.query('SELECT current_count FROM counting_game ORDER BY id DESC LIMIT 1');
      return result.rows[0] ? result.rows[0].current_count : 0;
    } catch (error) {
      console.error('Error getting current count:', error);
      return 0;
    }
  }

  async updateCount(newCount, userId, channelId, guildId) {
    try {
      const sql = `
        INSERT INTO counting_game (current_count, last_user_id, channel_id, guild_id, last_updated)
        VALUES ($1, $2, $3, $4, CURRENT_TIMESTAMP)
      `;
      
      await this.query(sql, [newCount, userId, channelId, guildId]);
      return true;
    } catch (error) {
      console.error('Error updating count:', error);
      return false;
    }
  }

  // Permission Management
  async grantPermission(entityId, entityType, permissionType, grantedBy) {
    try {
      const sql = `
        INSERT INTO permissions (entity_id, entity_type, permission_type, granted_by, granted_at, is_active)
        VALUES ($1, $2, $3, $4, CURRENT_TIMESTAMP, true)
        ON CONFLICT (entity_id, permission_type) 
        DO UPDATE SET is_active = true, granted_by = $4, granted_at = CURRENT_TIMESTAMP
      `;
      
      await this.query(sql, [entityId, entityType, permissionType, grantedBy]);
      return true;
    } catch (error) {
      console.error('Error granting permission:', error);
      return false;
    }
  }

  async revokePermission(entityId, permissionType) {
    try {
      const sql = `
        UPDATE permissions 
        SET is_active = false
        WHERE entity_id = $1 AND permission_type = $2
      `;
      
      await this.query(sql, [entityId, permissionType]);
      return true;
    } catch (error) {
      console.error('Error revoking permission:', error);
      return false;
    }
  }

  async hasPermission(entityId, permissionType) {
    try {
      const result = await this.query(
        'SELECT id FROM permissions WHERE entity_id = $1 AND permission_type = $2 AND is_active = true',
        [entityId, permissionType]
      );
      return result.rows.length > 0;
    } catch (error) {
      console.error('Error checking permission:', error);
      return false;
    }
  }

  // Settings Management
  async setSetting(key, value, type = 'string', description = null, updatedBy = null) {
    try {
      const sql = `
        INSERT INTO bot_settings (setting_key, setting_value, setting_type, description, updated_by, updated_at)
        VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP)
        ON CONFLICT (setting_key) 
        DO UPDATE SET setting_value = $2, setting_type = $3, description = $4, updated_by = $5, updated_at = CURRENT_TIMESTAMP
      `;
      
      await this.query(sql, [key, value, type, description, updatedBy]);
      return true;
    } catch (error) {
      console.error('Error setting value:', error);
      return false;
    }
  }

  async getSetting(key, defaultValue = null) {
    try {
      const result = await this.query('SELECT setting_value, setting_type FROM bot_settings WHERE setting_key = $1', [key]);
      
      if (result.rows.length === 0) return defaultValue;
      
      const { setting_value, setting_type } = result.rows[0];
      
      switch (setting_type) {
        case 'number':
          return parseFloat(setting_value);
        case 'boolean':
          return setting_value === 'true';
        case 'json':
          return JSON.parse(setting_value);
        default:
          return setting_value;
      }
    } catch (error) {
      console.error('Error getting setting:', error);
      return defaultValue;
    }
  }

  // Get leaderboard
  async getLeaderboard(limit = 10, orderBy = 'points') {
    try {
      const validOrderBy = ['points', 'wins', 'win_rate', 'games_played'];
      if (!validOrderBy.includes(orderBy)) {
        orderBy = 'points';
      }
      
      const sql = `
        SELECT user_id, username, points, wins, losses, games_played, win_rate, favorite_game
        FROM users 
        WHERE games_played > 0
        ORDER BY ${orderBy} DESC, points DESC
        LIMIT $1
      `;
      
      const result = await this.query(sql, [limit]);
      return result.rows;
    } catch (error) {
      console.error('Error getting leaderboard:', error);
      return [];
    }
  }

  // Close connection
  async close() {
    if (this.pool) {
      await this.pool.end();
      console.log('✅ PostgreSQL connection closed');
    }
  }
}

// Create singleton instance
const postgresDatabase = new PostgreSQLDatabase();

module.exports = {
  postgresDatabase
};
