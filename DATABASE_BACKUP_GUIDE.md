# 💾 دليل نظام النسخ الاحتياطية الشامل

## 📋 نظرة عامة

تم إنشاء نظام نسخ احتياطي شامل يحفظ جميع بيانات البوت تلقائياً ويمنع فقدان أي معلومات مهمة عند إعادة تشغيل البوت أو انقطاع الاتصال.

## 🎯 البيانات المحفوظة

### 📊 **النقاط واللوحة**
- نقاط جميع المستخدمين
- إحصائيات الألعاب (عدد الألعاب، الانتصارات، الخسائر)
- معدل الفوز لكل مستخدم
- تاريخ الانتصارات

### 🔢 **بيانات العد**
- الرقم الحالي في لعبة العد
- آخر تحديث للعداد
- تاريخ ووقت آخر عملية عد

### 🔐 **الصلاحيات والإعدادات**
- قائمة المستخدمين والأدوار المصرح لهم
- إعدادات البوت العامة
- إعدادات الألعاب المختلفة

### 🎮 **إحصائيات الألعاب**
- إجمالي الألعاب المُلعبة
- إحصائيات لكل نوع لعبة (روليت، كراسي، مافيا، جاسوس)
- قائمة الفائزين لكل لعبة
- تواريخ الألعاب

### 👥 **ملفات المستخدمين**
- تاريخ انضمام كل مستخدم
- اللعبة المفضلة لكل مستخدم
- آخر نشاط للمستخدم
- إجمالي الألعاب والانتصارات

## 🔧 الأوامر المتاحة

### إنشاء نسخة احتياطية
```
/نسخ_احتياطي
/backup
```
**الوصف:** ينشئ نسخة احتياطية فورية لجميع بيانات البوت.

**من يمكنه استخدامه:** المشرفين والمستخدمين المصرح لهم.

**مثال:**
```
/نسخ_احتياطي
```

### عرض قائمة النسخ الاحتياطية
```
/قائمة_النسخ
/list_backups
```
**الوصف:** يعرض قائمة بجميع النسخ الاحتياطية المتاحة مع تواريخها وأحجامها.

**مثال:**
```
/قائمة_النسخ
```

### استرداد نسخة احتياطية
```
/استرداد_نسخة filename.json
/restore filename.json
```
**الوصف:** يسترد جميع البيانات من نسخة احتياطية محددة.

**من يمكنه استخدامه:** المشرفين فقط.

**مثال:**
```
/استرداد_نسخة full_backup_2025-01-21T15-30-00-000Z.json
```

## ⚙️ النظام التلقائي

### 🔄 **النسخ التلقائي**
- ينشئ نسخة احتياطية كل **5 دقائق** تلقائياً
- يحفظ النسخ في مجلد `backups/`
- يحتفظ بآخر **50 نسخة احتياطية** فقط
- يحذف النسخ القديمة تلقائياً

### 🚨 **النسخ الطارئة**
- ينشئ نسخة احتياطية طارئة عند إغلاق البوت
- يحفظ البيانات قبل انقطاع الاتصال
- يضمن عدم فقدان أي بيانات

### 💾 **الحفظ الفوري**
- يحفظ البيانات فور تحديثها
- يحدث النسخ الاحتياطية في الوقت الفعلي
- يضمن تزامن جميع البيانات

## 📁 هيكل الملفات

```
ggm-main/
├── backups/                    # مجلد النسخ الاحتياطية
│   ├── full_backup_2025-01-21T15-30-00-000Z.json
│   ├── full_backup_2025-01-21T15-35-00-000Z.json
│   └── ...
├── data/                       # مجلد البيانات الإضافية
│   ├── game_stats.json         # إحصائيات الألعاب
│   ├── user_profiles.json      # ملفات المستخدمين
│   └── server_settings.json    # إعدادات الخادم
├── points.json                 # نقاط المستخدمين
├── counting_data.json          # بيانات لعبة العد
├── config.json                 # إعدادات البوت
└── database.js                 # نظام إدارة قاعدة البيانات
```

## 🔒 الأمان والحماية

### **التحقق من الصلاحيات**
- فقط المشرفين يمكنهم استرداد النسخ
- المستخدمين المصرح لهم يمكنهم إنشاء نسخ وعرضها
- حماية من الوصول غير المصرح

### **حماية البيانات**
- نسخ متعددة لضمان عدم الفقدان
- تشفير البيانات الحساسة
- نسخ احتياطية محلية آمنة

### **استرداد الأخطاء**
- نظام استرداد تلقائي عند فشل الملفات
- نسخ احتياطية للنسخ الاحتياطية
- سجلات مفصلة للأخطاء

## 🚀 الميزات المتقدمة

### **التكامل مع نظام النقاط**
- حفظ تلقائي عند تحديث النقاط
- تتبع إحصائيات الألعاب
- ربط البيانات بملفات المستخدمين

### **إحصائيات شاملة**
- تتبع جميع الألعاب المُلعبة
- إحصائيات مفصلة لكل مستخدم
- تقارير دورية عن أداء البوت

### **إدارة ذكية للمساحة**
- حذف تلقائي للنسخ القديمة
- ضغط البيانات لتوفير المساحة
- تنظيف دوري للملفات المؤقتة

## 📊 مثال على محتوى النسخة الاحتياطية

```json
{
  "timestamp": "2025-01-21T15:30:00.000Z",
  "version": "1.0.0",
  "data": {
    "points": {
      "123456789": {
        "points": 15,
        "stats": {
          "gamesPlayed": 10,
          "wins": 3,
          "losses": 7,
          "winRate": 30.0
        }
      }
    },
    "counting": {
      "currentCount": 1060,
      "lastUpdated": "2025-01-21T15:25:00.000Z",
      "timestamp": 1737460800000
    },
    "gameStats": {
      "totalGamesPlayed": 50,
      "gameTypes": {
        "roulette": { "played": 15, "winners": [...] },
        "chairs": { "played": 20, "winners": [...] }
      }
    }
  }
}
```

## 🛠️ استكشاف الأخطاء

### **مشاكل شائعة وحلولها**

**المشكلة:** "ليس لديك الإذن لإنشاء نسخة احتياطية"
**الحل:** تأكد من أن لديك صلاحية Administrator أو أنك مدرج في قائمة allowedRoleIds.

**المشكلة:** "Backup file not found"
**الحل:** تأكد من كتابة اسم الملف بشكل صحيح، استخدم `/قائمة_النسخ` لعرض الملفات المتاحة.

**المشكلة:** "حدث خطأ أثناء إنشاء النسخة الاحتياطية"
**الحل:** تحقق من مساحة القرص الصلب وصلاحيات الكتابة في مجلد البوت.

### **نصائح للاستخدام الأمثل**

1. **إنشاء نسخ يدوية قبل التحديثات المهمة**
2. **فحص قائمة النسخ بانتظام للتأكد من عملها**
3. **اختبار استرداد النسخ في بيئة تجريبية**
4. **الاحتفاظ بنسخ خارجية للبيانات المهمة**

## 📈 الإحصائيات والتقارير

### **تقرير حالة النظام**
- عدد النسخ الاحتياطية المتاحة
- آخر نسخة احتياطية تم إنشاؤها
- حجم البيانات المحفوظة
- معدل نجاح النسخ التلقائي

### **تقرير البيانات**
- عدد المستخدمين المسجلين
- إجمالي النقاط الموزعة
- عدد الألعاب المُلعبة
- أكثر الألعاب شعبية

## 🎯 الخلاصة

نظام النسخ الاحتياطي الشامل يضمن:
- **عدم فقدان أي بيانات** عند إعادة تشغيل البوت
- **استرداد سريع** للبيانات عند الحاجة
- **حفظ تلقائي** لجميع التحديثات
- **أمان عالي** للبيانات الحساسة
- **سهولة في الإدارة** والاستخدام

البوت الآن محمي بالكامل ضد فقدان البيانات! 🛡️
